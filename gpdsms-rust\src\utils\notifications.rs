use log::{debug, error};
use notify_rust::{Notification, Timeout};

pub struct NotificationManager;

impl NotificationManager {
    /// Send a desktop notification
    pub fn send_notification(title: &str, message: &str, timeout_seconds: u32) -> Result<(), Box<dyn std::error::Error>> {
        debug!("Sending notification: {} - {}", title, message);
        
        let timeout = if timeout_seconds > 0 {
            Timeout::Milliseconds(timeout_seconds * 1000)
        } else {
            Timeout::Default
        };
        
        #[cfg(target_os = "windows")]
        {
            Notification::new()
                .summary(title)
                .body(message)
                .icon("phone") // You can specify an icon path
                .timeout(timeout)
                .show()?;
        }
        
        #[cfg(not(target_os = "windows"))]
        {
            Notification::new()
                .summary(title)
                .body(message)
                .timeout(timeout)
                .show()?;
        }
        
        Ok(())
    }
    
    /// Send SMS received notification
    pub fn send_sms_notification(phone_number: &str, message: &str) -> Result<(), Box<dyn std::error::Error>> {
        let title = format!("来自 {} 的短信", phone_number);
        Self::send_notification(&title, message, 5)
    }
    
    /// Send SMS sent notification
    pub fn send_sms_sent_notification() -> Result<(), Box<dyn std::error::Error>> {
        Self::send_notification("短信发送", "短信已成功发送", 3)
    }
    
    /// Send error notification
    pub fn send_error_notification(error_message: &str) -> Result<(), Box<dyn std::error::Error>> {
        Self::send_notification("错误", error_message, 5)
    }
    
    /// Send connection notification
    pub fn send_connection_notification(connected: bool) -> Result<(), Box<dyn std::error::Error>> {
        let (title, message) = if connected {
            ("设备连接", "设备已成功连接")
        } else {
            ("设备断开", "设备连接已断开")
        };
        
        Self::send_notification(title, message, 3)
    }
}
