import { useState, useEffect } from 'react';
import { Layout, Button, Select, Card, message, Space, Typography, Spin } from 'antd';
import {
  PhoneOutlined,
  PlusOutlined,
  SettingOutlined,
  WifiOutlined,
  SignalFilled,
  ReloadOutlined
} from '@ant-design/icons';
import { invoke } from '@tauri-apps/api/core';

const { Header, Sider, Content } = Layout;
const { Option } = Select;
const { Title, Text } = Typography;

interface SerialPortInfo {
  port_name: string;
  description?: string;
  hardware_id?: string;
}

function App() {
  const [availablePorts, setAvailablePorts] = useState<SerialPortInfo[]>([]);
  const [selectedPort, setSelectedPort] = useState<string>('');
  const [connectionStatus, setConnectionStatus] = useState(false);
  const [connecting, setConnecting] = useState(false);
  const [signalStrength, setSignalStrength] = useState(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      await refreshPorts();
      await checkConnectionStatus();
    } catch (error) {
      console.error('初始化失败:', error);
      message.error('应用初始化失败');
    } finally {
      setLoading(false);
    }
  };

  const refreshPorts = async () => {
    try {
      const ports = await invoke<SerialPortInfo[]>('get_serial_ports');
      setAvailablePorts(ports);
    } catch (error) {
      message.error('获取串口列表失败: ' + error);
    }
  };

  const checkConnectionStatus = async () => {
    try {
      const status = await invoke<boolean>('get_connection_status');
      setConnectionStatus(status);
    } catch (error) {
      console.error('获取连接状态失败:', error);
    }
  };

  const toggleConnection = async () => {
    if (connectionStatus) {
      await disconnect();
    } else {
      await connect();
    }
  };

  const connect = async () => {
    if (!selectedPort) {
      message.warning('请选择串口');
      return;
    }

    setConnecting(true);
    try {
      await invoke('connect_serial', { portName: selectedPort });
      setConnectionStatus(true);
      message.success('连接成功');
    } catch (error) {
      message.error('连接失败: ' + error);
    } finally {
      setConnecting(false);
    }
  };

  const disconnect = async () => {
    setConnecting(true);
    try {
      await invoke('disconnect_serial');
      setConnectionStatus(false);
      setSignalStrength(0);
      message.success('已断开连接');
    } catch (error) {
      message.error('断开连接失败: ' + error);
    } finally {
      setConnecting(false);
    }
  };

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh'
      }}>
        <Spin size="large" />
        <Text style={{ marginLeft: 16 }}>正在初始化应用...</Text>
      </div>
    );
  }

  return (
    <Layout style={{ height: '100vh' }}>
      {/* Header */}
      <Header style={{
        background: '#1890ff',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '0 20px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <PhoneOutlined style={{ fontSize: '20px' }} />
          <Title level={4} style={{ color: 'white', margin: 0 }}>
            GPDSMS-Rust
          </Title>
        </div>

        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            style={{ background: '#52c41a', borderColor: '#52c41a' }}
          >
            新短信
          </Button>

          <Button type="text" icon={<SettingOutlined />} style={{ color: 'white' }} />
        </Space>
      </Header>

      <Layout>
        {/* Sidebar */}
        <Sider
          width={300}
          style={{ background: '#f5f5f5', padding: '20px' }}
        >
          {/* Connection Status */}
          <Card size="small" style={{ marginBottom: '16px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <WifiOutlined
                  style={{
                    color: connectionStatus ? '#52c41a' : '#ff4d4f',
                    fontSize: '16px'
                  }}
                />
                <span>{connectionStatus ? '已连接' : '未连接'}</span>
              </div>
              <Button
                size="small"
                type={connectionStatus ? 'default' : 'primary'}
                danger={connectionStatus}
                onClick={toggleConnection}
                loading={connecting}
              >
                {connectionStatus ? '断开' : '连接'}
              </Button>
            </div>
          </Card>

          {/* Port Selection */}
          {!connectionStatus && (
            <Card size="small" style={{ marginBottom: '16px' }}>
              <div style={{ marginBottom: '8px' }}>选择串口:</div>
              <Select
                value={selectedPort}
                onChange={setSelectedPort}
                onFocus={refreshPorts}
                style={{ width: '100%', marginBottom: '8px' }}
                placeholder="选择串口"
              >
                {availablePorts.map(port => (
                  <Option key={port.port_name} value={port.port_name}>
                    {port.port_name} {port.description && `(${port.description})`}
                  </Option>
                ))}
              </Select>
              <Button
                size="small"
                icon={<ReloadOutlined />}
                onClick={refreshPorts}
                style={{ width: '100%' }}
              >
                刷新端口
              </Button>
            </Card>
          )}

          {/* Signal Strength */}
          {connectionStatus && (
            <Card size="small">
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <SignalFilled style={{ color: '#1890ff' }} />
                <span>信号强度: {signalStrength}%</span>
              </div>
            </Card>
          )}
        </Sider>

        {/* Main Content */}
        <Content style={{ padding: '20px', background: '#fff' }}>
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            flexDirection: 'column',
            gap: '20px'
          }}>
            <Title level={2} style={{ color: '#666' }}>
              欢迎使用 GPDSMS-Rust
            </Title>
            <Text type="secondary" style={{ fontSize: '16px' }}>
              基于 Tauri 2 + React 开发的现代化短信管理工具
            </Text>

            {!connectionStatus && (
              <div style={{ textAlign: 'center' }}>
                <Text type="secondary">
                  请在左侧选择串口并连接设备开始使用
                </Text>
              </div>
            )}

            {connectionStatus && (
              <div style={{ textAlign: 'center' }}>
                <Text style={{ color: '#52c41a', fontSize: '16px' }}>
                  ✅ 设备已连接，可以开始收发短信
                </Text>
              </div>
            )}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
}

export default App;
