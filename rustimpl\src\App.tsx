import { useState, useEffect } from 'react';
import { Layout, Button, Select, Card, message, Space, Typography, Spin, Modal, List, Input, Form } from 'antd';
import {
  PhoneOutlined,
  PlusOutlined,
  SettingOutlined,
  WifiOutlined,
  SignalFilled,
  ReloadOutlined,
  BugOutlined,
  SendOutlined,
  DeleteOutlined,
  SearchOutlined
} from '@ant-design/icons';
import { invoke } from '@tauri-apps/api/core';

const { Header, Sider, Content } = Layout;
const { Option } = Select;
const { Title, Text } = Typography;

interface SerialPortInfo {
  port_name: string;
  description?: string;
  hardware_id?: string;
}

interface SmsMessage {
  id?: number;
  phone_no: string;
  phone_to: string;
  message: string;
  create_date: string;
  message_type: 'Received' | 'Sent';
  batch_no: number;
  order_no: number;
}

interface SmsHistoryResponse {
  messages: SmsMessage[];
  total_count: number;
}

function App() {
  const [availablePorts, setAvailablePorts] = useState<SerialPortInfo[]>([]);
  const [selectedPort, setSelectedPort] = useState<string>('');
  const [connectionStatus, setConnectionStatus] = useState(false);
  const [connecting, setConnecting] = useState(false);
  const [signalStrength, setSignalStrength] = useState(0);
  const [loading, setLoading] = useState(true);

  // SMS related states
  const [smsHistory, setSmsHistory] = useState<SmsMessage[]>([]);
  const [loadingSms, setLoadingSms] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showSendModal, setShowSendModal] = useState(false);
  const [sendForm] = Form.useForm();

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      await refreshPorts();
      await checkConnectionStatus();

      // 如果已经连接，加载短信历史
      try {
        const status = await invoke<boolean>('get_connection_status');
        if (status) {
          setConnectionStatus(true);
          await loadSmsHistory();
        }
      } catch (error) {
        console.warn('Failed to check connection status or load SMS history:', error);
        // 不显示错误，因为这不是致命错误
      }

      console.log('应用初始化完成，等待用户操作');
    } catch (error) {
      console.error('初始化失败:', error);
      message.error('应用初始化失败');
    } finally {
      setLoading(false);
    }
  };

  const autoConnectGSM = async () => {
    try {
      setConnecting(true);
      console.log('开始自动连接GSM设备...');

      // 显示带进度的加载消息
      const loadingKey = 'auto-connect';
      message.loading({ content: '正在扫描串口...', key: loadingKey, duration: 0 });

      // 添加超时处理 - 增加到60秒，因为设备检测可能需要更长时间
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('连接超时')), 60000); // 60秒超时
      });

      // 更新进度消息
      setTimeout(() => {
        message.loading({ content: '正在检测GSM设备...', key: loadingKey, duration: 0 });
      }, 2000);

      setTimeout(() => {
        message.loading({ content: '正在测试设备通信...', key: loadingKey, duration: 0 });
      }, 10000);

      const connectPromise = invoke('auto_connect_gsm');

      const result = await Promise.race([connectPromise, timeoutPromise]);

      message.destroy(loadingKey);
      message.success('设备连接成功！');
      console.log('Auto-connect result:', result);

      setConnectionStatus(true);
      await checkConnectionStatus(); // 刷新连接状态

      // 连接成功后，给用户选择是否加载短信
      setTimeout(() => {
        Modal.confirm({
          title: '连接成功',
          content: '是否要从SIM卡加载短信历史记录？',
          onOk: async () => {
            try {
              await loadSMSFromSIM();
            } catch (error) {
              console.error('加载短信失败:', error);
              message.error('加载短信失败，但设备连接正常');
            }
          },
          onCancel: () => {
            message.info('已跳过短信加载，您可以稍后手动加载');
          }
        });
      }, 1000); // 延迟1秒显示，确保连接状态已更新
    } catch (error) {
      message.destroy();
      console.error('自动连接失败:', error);

      // 显示具体的错误信息
      if (error instanceof Error) {
        if (error.message.includes('超时')) {
          message.error('连接超时，请尝试以下解决方案：\n1. 检查GSM设备是否已连接\n2. 确认设备驱动已安装\n3. 尝试重新插拔设备');
        } else if (error.message.includes('No GSM modem detected')) {
          message.warning('未检测到GSM设备，请检查设备连接');
        } else {
          message.error(`连接失败: ${error.message}`);
        }
      } else {
        message.error('连接失败: ' + String(error));
      }
    } finally {
      setConnecting(false);
    }
  };

  const loadSMSFromSIM = async () => {
    try {
      setLoadingSms(true);
      message.loading('正在从SIM卡加载短信...', 0);
      const result = await invoke('load_sms_from_sim');
      message.destroy();
      message.success(result as string);
      console.log('SMS load result:', result);

      // 加载完成后刷新短信历史
      await loadSmsHistory();
    } catch (error) {
      message.destroy();
      console.warn('加载短信失败:', error);
      message.error('加载短信失败: ' + error);
    } finally {
      setLoadingSms(false);
    }
  };

  const loadSmsHistory = async () => {
    try {
      setLoadingSms(true);
      console.log('Loading SMS history...');

      const response = await invoke<SmsHistoryResponse>('get_sms_history', {
        limit: 100,
        offset: 0
      });

      console.log('SMS history response:', response);

      // 确保返回的数据结构正确
      if (response && Array.isArray(response.messages)) {
        setSmsHistory(response.messages);
        console.log('Loaded SMS history:', response.messages.length, 'messages');
        if (response.messages.length > 0) {
          message.success(`加载了 ${response.messages.length} 条短信记录`);
        }
      } else {
        console.warn('SMS history response is invalid:', response);
        setSmsHistory([]);
      }
    } catch (error) {
      console.error('Failed to load SMS history:', error);
      setSmsHistory([]); // 设置为空数组而不是显示错误
      message.error('加载短信历史失败: ' + error);
    } finally {
      setLoadingSms(false);
    }
  };

  const sendSms = async (phoneNumber: string, messageText: string) => {
    try {
      message.loading('正在发送短信...', 0);
      const result = await invoke('send_sms', {
        request: {
          phone_number: phoneNumber,
          message: messageText
        }
      });
      message.destroy();
      message.success('短信发送成功');
      console.log('SMS send result:', result);

      // 发送成功后刷新短信历史
      await loadSmsHistory();
      setShowSendModal(false);
      sendForm.resetFields();
    } catch (error) {
      message.destroy();
      message.error('发送短信失败: ' + error);
    }
  };

  const deleteSms = async (id: number) => {
    try {
      await invoke('delete_sms', { id });
      message.success('短信删除成功');
      await loadSmsHistory();
    } catch (error) {
      message.error('删除短信失败: ' + error);
    }
  };

  const searchSms = async (term: string) => {
    if (!term.trim()) {
      await loadSmsHistory();
      return;
    }

    try {
      setLoadingSms(true);
      const results = await invoke<SmsMessage[]>('search_sms', {
        searchTerm: term,
        limit: 100
      });

      // 确保返回的是数组
      if (Array.isArray(results)) {
        setSmsHistory(results);
      } else {
        console.warn('Search results is not an array:', results);
        setSmsHistory([]);
      }
    } catch (error) {
      console.error('Search failed:', error);
      setSmsHistory([]);
      message.error('搜索失败: ' + error);
    } finally {
      setLoadingSms(false);
    }
  };

  const runDiagnosis = async () => {
    try {
      message.loading('正在运行连接诊断...', 0);
      const result = await invoke<string>('diagnose_connection');
      message.destroy();

      // 显示诊断结果在模态框中
      Modal.info({
        title: '连接诊断结果',
        content: (
          <pre style={{
            whiteSpace: 'pre-wrap',
            fontSize: '12px',
            maxHeight: '400px',
            overflow: 'auto'
          }}>
            {result}
          </pre>
        ),
        width: 600,
      });
    } catch (error) {
      message.destroy();
      message.error('诊断失败: ' + error);
    }
  };

  const refreshPorts = async () => {
    try {
      const ports = await invoke<SerialPortInfo[]>('get_serial_ports');
      setAvailablePorts(ports);
    } catch (error) {
      message.error('获取串口列表失败: ' + error);
    }
  };

  const checkConnectionStatus = async () => {
    try {
      const status = await invoke<boolean>('get_connection_status');
      setConnectionStatus(status);
    } catch (error) {
      console.error('获取连接状态失败:', error);
    }
  };

  const toggleConnection = async () => {
    if (connectionStatus) {
      await disconnect();
    } else {
      await connect();
    }
  };

  const connect = async () => {
    if (!selectedPort) {
      message.warning('请选择串口');
      return;
    }

    setConnecting(true);
    try {
      await invoke('connect_serial', { portName: selectedPort });
      setConnectionStatus(true);
      message.success('连接成功');
    } catch (error) {
      message.error('连接失败: ' + error);
    } finally {
      setConnecting(false);
    }
  };

  const disconnect = async () => {
    setConnecting(true);
    try {
      await invoke('disconnect_serial');
      setConnectionStatus(false);
      setSignalStrength(0);
      message.success('已断开连接');
    } catch (error) {
      message.error('断开连接失败: ' + error);
    } finally {
      setConnecting(false);
    }
  };

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh'
      }}>
        <Spin size="large" />
        <Text style={{ marginLeft: 16 }}>正在初始化应用...</Text>
      </div>
    );
  }

  return (
    <Layout style={{ height: '100vh' }}>
      {/* Header */}
      <Header style={{
        background: '#1890ff',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '0 20px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <PhoneOutlined style={{ fontSize: '20px' }} />
          <Title level={4} style={{ color: 'white', margin: 0 }}>
            GPDSMS-Rust
          </Title>
        </div>

        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setShowSendModal(true)}
            disabled={!connectionStatus}
            style={{ background: '#52c41a', borderColor: '#52c41a' }}
          >
            新短信
          </Button>

          <Button type="text" icon={<SettingOutlined />} style={{ color: 'white' }} />
        </Space>
      </Header>

      <Layout>
        {/* Sidebar */}
        <Sider
          width={300}
          style={{ background: '#f5f5f5', padding: '20px' }}
        >
          {/* Connection Status */}
          <Card size="small" style={{ marginBottom: '16px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <WifiOutlined
                  style={{
                    color: connectionStatus ? '#52c41a' : '#ff4d4f',
                    fontSize: '16px'
                  }}
                />
                <span>{connectionStatus ? '已连接' : '未连接'}</span>
              </div>
              <Button
                size="small"
                type={connectionStatus ? 'default' : 'primary'}
                danger={connectionStatus}
                onClick={toggleConnection}
                loading={connecting}
              >
                {connectionStatus ? '断开' : '连接'}
              </Button>
            </div>
          </Card>

          {/* Port Selection */}
          {!connectionStatus && (
            <Card size="small" style={{ marginBottom: '16px' }}>
              <div style={{ marginBottom: '8px' }}>连接选项:</div>

              {/* Auto Connect Button */}
              <Button
                type="primary"
                icon={<WifiOutlined />}
                onClick={autoConnectGSM}
                loading={connecting}
                style={{ width: '100%', marginBottom: '8px' }}
              >
                自动检测并连接
              </Button>

              <div style={{ marginBottom: '8px', fontSize: '12px', color: '#666' }}>
                或手动选择串口:
              </div>

              <Select
                value={selectedPort}
                onChange={setSelectedPort}
                onFocus={refreshPorts}
                style={{ width: '100%', marginBottom: '8px' }}
                placeholder="选择串口"
              >
                {availablePorts.map(port => (
                  <Option key={port.port_name} value={port.port_name}>
                    {port.port_name} {port.description && `(${port.description})`}
                  </Option>
                ))}
              </Select>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={refreshPorts}
                  style={{ width: '100%' }}
                >
                  刷新端口
                </Button>

                <Button
                  size="small"
                  icon={<BugOutlined />}
                  onClick={runDiagnosis}
                  style={{ width: '100%' }}
                >
                  连接诊断
                </Button>
              </Space>
            </Card>
          )}

          {/* Signal Strength and SMS Actions */}
          {connectionStatus && (
            <>
              <Card size="small" style={{ marginBottom: '16px' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <SignalFilled style={{ color: '#1890ff' }} />
                  <span>信号强度: {signalStrength}%</span>
                </div>
              </Card>

              <Card size="small" title="短信管理">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Button
                    type="primary"
                    icon={<ReloadOutlined />}
                    onClick={loadSMSFromSIM}
                    style={{ width: '100%' }}
                  >
                    重新加载短信
                  </Button>

                  <Button
                    icon={<PlusOutlined />}
                    onClick={() => setShowSendModal(true)}
                    style={{ width: '100%' }}
                  >
                    发送新短信
                  </Button>
                </Space>
              </Card>
            </>
          )}
        </Sider>

        {/* Main Content */}
        <Content style={{ padding: '20px', background: '#fff' }}>
          {!connectionStatus ? (
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
              flexDirection: 'column',
              gap: '20px'
            }}>
              <Title level={2} style={{ color: '#666' }}>
                欢迎使用 GPDSMS-Rust
              </Title>
              <Text type="secondary" style={{ fontSize: '16px' }}>
                基于 Tauri 2 + React 开发的现代化短信管理工具
              </Text>
              <div style={{ textAlign: 'center' }}>
                <Text type="secondary">
                  请在左侧选择串口并连接设备开始使用
                </Text>
              </div>
            </div>
          ) : (
            <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              {/* SMS Header */}
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '16px',
                padding: '0 8px'
              }}>
                <Title level={3} style={{ margin: 0 }}>
                  短信历史 ({smsHistory?.length || 0})
                </Title>
                <Space>
                  <Input.Search
                    placeholder="搜索短信..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onSearch={searchSms}
                    style={{ width: 200 }}
                    allowClear
                  />
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={loadSmsHistory}
                    loading={loadingSms}
                  >
                    刷新
                  </Button>
                  <Button
                    icon={<BugOutlined />}
                    onClick={async () => {
                      try {
                        const info = await invoke<string>('debug_database_info');
                        Modal.info({
                          title: '数据库调试信息',
                          content: <pre>{info}</pre>,
                          width: 600,
                        });
                      } catch (error) {
                        message.error('获取调试信息失败: ' + error);
                      }
                    }}
                  >
                    调试
                  </Button>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => setShowSendModal(true)}
                  >
                    发送短信
                  </Button>
                </Space>
              </div>

              {/* SMS List */}
              <div style={{ flex: 1, overflow: 'hidden' }}>
                <List
                  loading={loadingSms}
                  dataSource={smsHistory || []}
                  locale={{ emptyText: '暂无短信记录' }}
                  renderItem={(sms) => (
                    <List.Item
                      actions={[
                        <Button
                          key="delete"
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => {
                            Modal.confirm({
                              title: '确认删除',
                              content: '确定要删除这条短信吗？',
                              onOk: () => deleteSms(sms.id!),
                            });
                          }}
                        />
                      ]}
                    >
                      <List.Item.Meta
                        title={
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <span style={{
                              color: sms.message_type === 'Received' ? '#1890ff' : '#52c41a',
                              fontWeight: 'bold'
                            }}>
                              {sms.message_type === 'Received' ? '📥' : '📤'} {sms.phone_no}
                            </span>
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              {new Date(sms.create_date).toLocaleString()}
                            </Text>
                          </div>
                        }
                        description={
                          <div style={{
                            marginTop: '8px',
                            padding: '8px 12px',
                            background: sms.message_type === 'Received' ? '#f0f8ff' : '#f6ffed',
                            borderRadius: '8px',
                            border: `1px solid ${sms.message_type === 'Received' ? '#d6e4ff' : '#d9f7be'}`
                          }}>
                            {sms.message}
                          </div>
                        }
                      />
                    </List.Item>
                  )}
                  style={{
                    height: '100%',
                    overflow: 'auto',
                    background: '#fafafa',
                    borderRadius: '8px',
                    padding: '8px'
                  }}
                />
              </div>
            </div>
          )}
        </Content>
      </Layout>

      {/* Send SMS Modal */}
      <Modal
        title="发送短信"
        open={showSendModal}
        onCancel={() => {
          setShowSendModal(false);
          sendForm.resetFields();
        }}
        footer={null}
      >
        <Form
          form={sendForm}
          layout="vertical"
          onFinish={(values) => {
            sendSms(values.phoneNumber, values.message);
          }}
        >
          <Form.Item
            name="phoneNumber"
            label="手机号码"
            rules={[
              { required: true, message: '请输入手机号码' },
              { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' }
            ]}
          >
            <Input placeholder="请输入手机号码" />
          </Form.Item>

          <Form.Item
            name="message"
            label="短信内容"
            rules={[
              { required: true, message: '请输入短信内容' },
              { max: 160, message: '短信内容不能超过160个字符' }
            ]}
          >
            <Input.TextArea
              rows={4}
              placeholder="请输入短信内容"
              showCount
              maxLength={160}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setShowSendModal(false);
                sendForm.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" icon={<SendOutlined />}>
                发送
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </Layout>
  );
}

export default App;
