import { useState, useEffect } from 'react';
import { Layout, Button, Select, Card, message, Space, Typography, Spin, Modal } from 'antd';
import {
  PhoneOutlined,
  PlusOutlined,
  SettingOutlined,
  WifiOutlined,
  SignalFilled,
  ReloadOutlined,
  BugOutlined
} from '@ant-design/icons';
import { invoke } from '@tauri-apps/api/core';

const { Header, Sider, Content } = Layout;
const { Option } = Select;
const { Title, Text } = Typography;

interface SerialPortInfo {
  port_name: string;
  description?: string;
  hardware_id?: string;
}

function App() {
  const [availablePorts, setAvailablePorts] = useState<SerialPortInfo[]>([]);
  const [selectedPort, setSelectedPort] = useState<string>('');
  const [connectionStatus, setConnectionStatus] = useState(false);
  const [connecting, setConnecting] = useState(false);
  const [signalStrength, setSignalStrength] = useState(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      await refreshPorts();
      await checkConnectionStatus();

      // 不在启动时自动连接，让用户手动选择
      console.log('应用初始化完成，等待用户操作');
    } catch (error) {
      console.error('初始化失败:', error);
      message.error('应用初始化失败');
    } finally {
      setLoading(false);
    }
  };

  const autoConnectGSM = async () => {
    try {
      setConnecting(true);
      console.log('开始自动连接GSM设备...');

      // 显示带进度的加载消息
      const loadingKey = 'auto-connect';
      message.loading({ content: '正在扫描串口...', key: loadingKey, duration: 0 });

      // 添加超时处理 - 增加到60秒，因为设备检测可能需要更长时间
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('连接超时')), 60000); // 60秒超时
      });

      // 更新进度消息
      setTimeout(() => {
        message.loading({ content: '正在检测GSM设备...', key: loadingKey, duration: 0 });
      }, 2000);

      setTimeout(() => {
        message.loading({ content: '正在测试设备通信...', key: loadingKey, duration: 0 });
      }, 10000);

      const connectPromise = invoke('auto_connect_gsm');

      const result = await Promise.race([connectPromise, timeoutPromise]);

      message.destroy(loadingKey);
      message.success('设备连接成功！');
      console.log('Auto-connect result:', result);

      // 连接成功后自动加载短信
      await loadSMSFromSIM();

      setConnectionStatus(true);
      await checkConnectionStatus(); // 刷新连接状态
    } catch (error) {
      message.destroy();
      console.error('自动连接失败:', error);

      // 显示具体的错误信息
      if (error instanceof Error) {
        if (error.message.includes('超时')) {
          message.error('连接超时，请尝试以下解决方案：\n1. 检查GSM设备是否已连接\n2. 确认设备驱动已安装\n3. 尝试重新插拔设备');
        } else if (error.message.includes('No GSM modem detected')) {
          message.warning('未检测到GSM设备，请检查设备连接');
        } else {
          message.error(`连接失败: ${error.message}`);
        }
      } else {
        message.error('连接失败: ' + String(error));
      }
    } finally {
      setConnecting(false);
    }
  };

  const loadSMSFromSIM = async () => {
    try {
      message.loading('正在从SIM卡加载短信...', 0);
      const result = await invoke('load_sms_from_sim');
      message.destroy();
      message.success(result as string);
      console.log('SMS load result:', result);
    } catch (error) {
      message.destroy();
      console.warn('加载短信失败:', error);
      // 不显示错误，因为可能SIM卡中没有短信
    }
  };

  const runDiagnosis = async () => {
    try {
      message.loading('正在运行连接诊断...', 0);
      const result = await invoke<string>('diagnose_connection');
      message.destroy();

      // 显示诊断结果在模态框中
      Modal.info({
        title: '连接诊断结果',
        content: (
          <pre style={{
            whiteSpace: 'pre-wrap',
            fontSize: '12px',
            maxHeight: '400px',
            overflow: 'auto'
          }}>
            {result}
          </pre>
        ),
        width: 600,
      });
    } catch (error) {
      message.destroy();
      message.error('诊断失败: ' + error);
    }
  };

  const refreshPorts = async () => {
    try {
      const ports = await invoke<SerialPortInfo[]>('get_serial_ports');
      setAvailablePorts(ports);
    } catch (error) {
      message.error('获取串口列表失败: ' + error);
    }
  };

  const checkConnectionStatus = async () => {
    try {
      const status = await invoke<boolean>('get_connection_status');
      setConnectionStatus(status);
    } catch (error) {
      console.error('获取连接状态失败:', error);
    }
  };

  const toggleConnection = async () => {
    if (connectionStatus) {
      await disconnect();
    } else {
      await connect();
    }
  };

  const connect = async () => {
    if (!selectedPort) {
      message.warning('请选择串口');
      return;
    }

    setConnecting(true);
    try {
      await invoke('connect_serial', { portName: selectedPort });
      setConnectionStatus(true);
      message.success('连接成功');
    } catch (error) {
      message.error('连接失败: ' + error);
    } finally {
      setConnecting(false);
    }
  };

  const disconnect = async () => {
    setConnecting(true);
    try {
      await invoke('disconnect_serial');
      setConnectionStatus(false);
      setSignalStrength(0);
      message.success('已断开连接');
    } catch (error) {
      message.error('断开连接失败: ' + error);
    } finally {
      setConnecting(false);
    }
  };

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh'
      }}>
        <Spin size="large" />
        <Text style={{ marginLeft: 16 }}>正在初始化应用...</Text>
      </div>
    );
  }

  return (
    <Layout style={{ height: '100vh' }}>
      {/* Header */}
      <Header style={{
        background: '#1890ff',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '0 20px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <PhoneOutlined style={{ fontSize: '20px' }} />
          <Title level={4} style={{ color: 'white', margin: 0 }}>
            GPDSMS-Rust
          </Title>
        </div>

        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            style={{ background: '#52c41a', borderColor: '#52c41a' }}
          >
            新短信
          </Button>

          <Button type="text" icon={<SettingOutlined />} style={{ color: 'white' }} />
        </Space>
      </Header>

      <Layout>
        {/* Sidebar */}
        <Sider
          width={300}
          style={{ background: '#f5f5f5', padding: '20px' }}
        >
          {/* Connection Status */}
          <Card size="small" style={{ marginBottom: '16px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <WifiOutlined
                  style={{
                    color: connectionStatus ? '#52c41a' : '#ff4d4f',
                    fontSize: '16px'
                  }}
                />
                <span>{connectionStatus ? '已连接' : '未连接'}</span>
              </div>
              <Button
                size="small"
                type={connectionStatus ? 'default' : 'primary'}
                danger={connectionStatus}
                onClick={toggleConnection}
                loading={connecting}
              >
                {connectionStatus ? '断开' : '连接'}
              </Button>
            </div>
          </Card>

          {/* Port Selection */}
          {!connectionStatus && (
            <Card size="small" style={{ marginBottom: '16px' }}>
              <div style={{ marginBottom: '8px' }}>连接选项:</div>

              {/* Auto Connect Button */}
              <Button
                type="primary"
                icon={<WifiOutlined />}
                onClick={autoConnectGSM}
                loading={connecting}
                style={{ width: '100%', marginBottom: '8px' }}
              >
                自动检测并连接
              </Button>

              <div style={{ marginBottom: '8px', fontSize: '12px', color: '#666' }}>
                或手动选择串口:
              </div>

              <Select
                value={selectedPort}
                onChange={setSelectedPort}
                onFocus={refreshPorts}
                style={{ width: '100%', marginBottom: '8px' }}
                placeholder="选择串口"
              >
                {availablePorts.map(port => (
                  <Option key={port.port_name} value={port.port_name}>
                    {port.port_name} {port.description && `(${port.description})`}
                  </Option>
                ))}
              </Select>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={refreshPorts}
                  style={{ width: '100%' }}
                >
                  刷新端口
                </Button>

                <Button
                  size="small"
                  icon={<BugOutlined />}
                  onClick={runDiagnosis}
                  style={{ width: '100%' }}
                >
                  连接诊断
                </Button>
              </Space>
            </Card>
          )}

          {/* Signal Strength and SMS Actions */}
          {connectionStatus && (
            <>
              <Card size="small" style={{ marginBottom: '16px' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <SignalFilled style={{ color: '#1890ff' }} />
                  <span>信号强度: {signalStrength}%</span>
                </div>
              </Card>

              <Card size="small" title="短信管理">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Button
                    type="primary"
                    icon={<ReloadOutlined />}
                    onClick={loadSMSFromSIM}
                    style={{ width: '100%' }}
                  >
                    重新加载短信
                  </Button>

                  <Button
                    icon={<PlusOutlined />}
                    style={{ width: '100%' }}
                  >
                    发送新短信
                  </Button>
                </Space>
              </Card>
            </>
          )}
        </Sider>

        {/* Main Content */}
        <Content style={{ padding: '20px', background: '#fff' }}>
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            flexDirection: 'column',
            gap: '20px'
          }}>
            <Title level={2} style={{ color: '#666' }}>
              欢迎使用 GPDSMS-Rust
            </Title>
            <Text type="secondary" style={{ fontSize: '16px' }}>
              基于 Tauri 2 + React 开发的现代化短信管理工具
            </Text>

            {!connectionStatus && (
              <div style={{ textAlign: 'center' }}>
                <Text type="secondary">
                  请在左侧选择串口并连接设备开始使用
                </Text>
              </div>
            )}

            {connectionStatus && (
              <div style={{ textAlign: 'center' }}>
                <Text style={{ color: '#52c41a', fontSize: '16px' }}>
                  ✅ 设备已连接，可以开始收发短信
                </Text>
              </div>
            )}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
}

export default App;
