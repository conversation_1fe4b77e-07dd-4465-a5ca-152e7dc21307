use crate::core::serial_manager::SerialManager;
use crate::core::types::{SerialPortInfo, SmsMessage, SmsType};
use crate::gui::app_state::AppState;
use log::{debug, info, warn};
use serde::{Deserialize, Serialize};
use tauri::State;

#[derive(Debug, Serialize, Deserialize)]
pub struct SendSmsRequest {
    pub phone_number: String,
    pub message: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SmsHistoryResponse {
    pub messages: Vec<SmsMessage>,
    pub total_count: u32,
}

/// Get available serial ports
#[tauri::command]
pub async fn get_serial_ports() -> Result<Vec<SerialPortInfo>, String> {
    crate::core::serial_manager::SerialManager::get_available_ports()
        .await
        .map_err(|e| e.to_string())
}

/// Connect to serial port
#[tauri::command]
pub async fn connect_serial(
    port_name: String,
    state: State<'_, AppState>,
) -> Result<(), String> {
    let mut serial_manager = state.serial_manager.lock().await;

    // Clear any existing connection
    if serial_manager.is_connected() {
        serial_manager.disconnect().await.map_err(|e| e.to_string())?;
    }

    // Connect to the port
    serial_manager.connect(&port_name).await.map_err(|e| e.to_string())?;

    // Clear buffer and initialize modem after connection
    serial_manager.clear_buffer().await.map_err(|e| e.to_string())?;
    serial_manager.initialize_modem().await.map_err(|e| e.to_string())?;

    Ok(())
}

/// Disconnect from serial port
#[tauri::command]
pub async fn disconnect_serial(state: State<'_, AppState>) -> Result<(), String> {
    let mut serial_manager = state.serial_manager.lock().await;
    serial_manager.disconnect().await.map_err(|e| e.to_string())
}

/// Get connection status
#[tauri::command]
pub async fn get_connection_status(state: State<'_, AppState>) -> Result<bool, String> {
    let serial_manager = state.serial_manager.lock().await;
    Ok(serial_manager.is_connected())
}

/// Test connection and get diagnostics
#[tauri::command]
pub async fn test_connection(state: State<'_, AppState>) -> Result<String, String> {
    let serial_manager = state.serial_manager.lock().await;
    if !serial_manager.is_connected() {
        return Ok("Not connected to any serial port".to_string());
    }

    serial_manager.test_connection().await.map_err(|e| e.to_string())
}

/// Auto-detect and connect to GSM modem (like original C# code)
#[tauri::command]
pub async fn auto_connect_gsm(state: State<'_, AppState>) -> Result<String, String> {
    info!("Starting auto-detection of GSM modem...");

    // First try to auto-detect GSM port
    let port_name = match SerialManager::auto_detect_gsm_port().await {
        Ok(Some(port)) => {
            info!("Auto-detected GSM modem on port: {}", port);
            port
        }
        Ok(None) => {
            return Err("No GSM modem detected. Please check device connection.".to_string());
        }
        Err(e) => {
            return Err(format!("Failed to detect GSM modem: {}", e));
        }
    };

    // Connect to the detected port
    let mut serial_manager = state.serial_manager.lock().await;

    // Clear any existing connection
    if serial_manager.is_connected() {
        serial_manager.disconnect().await.map_err(|e| e.to_string())?;
    }

    // Connect to the auto-detected port
    serial_manager.connect(&port_name).await.map_err(|e| e.to_string())?;

    // Clear buffer and initialize modem
    serial_manager.clear_buffer().await.map_err(|e| e.to_string())?;
    serial_manager.initialize_modem().await.map_err(|e| e.to_string())?;

    // Get modem info for confirmation
    let modem_info = serial_manager.test_connection().await.map_err(|e| e.to_string())?;

    info!("Successfully auto-connected to GSM modem on {}", port_name);
    Ok(format!("Connected to {} successfully!\n\n{}", port_name, modem_info))
}

/// Send SMS message
#[tauri::command]
pub async fn send_sms(
    request: SendSmsRequest,
    state: State<'_, AppState>,
) -> Result<(), String> {
    let serial_manager = state.serial_manager.lock().await;
    let sms_manager = state.sms_manager.lock().await;
    let database = state.database.lock().await;

    // Send SMS
    let _results = sms_manager
        .send_sms(&*serial_manager, &request.phone_number, &request.message)
        .await
        .map_err(|e| e.to_string())?;

    // Save to database
    let sms_message = SmsMessage {
        id: None,
        phone_no: String::new(), // Current user's number (unknown)
        phone_to: request.phone_number,
        message: request.message,
        create_date: std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs()
            .to_string(),
        message_type: SmsType::Sent,
        batch_no: 0,
        order_no: 0,
    };

    database.save_sms(&sms_message).await.map_err(|e| e.to_string())?;

    Ok(())
}

/// Get SMS history
#[tauri::command]
pub async fn get_sms_history(
    limit: u32,
    offset: u32,
    state: State<'_, AppState>,
) -> Result<SmsHistoryResponse, String> {
    let database = state.database.lock().await;

    let messages = database
        .get_sms_history(limit, offset)
        .await
        .map_err(|e| e.to_string())?;

    // For now, return the count of retrieved messages as total
    // In a real implementation, you'd want a separate count query
    let total_count = messages.len() as u32;

    Ok(SmsHistoryResponse {
        messages,
        total_count,
    })
}

/// Delete SMS message
#[tauri::command]
pub async fn delete_sms(id: i64, state: State<'_, AppState>) -> Result<bool, String> {
    let database = state.database.lock().await;
    database.delete_sms(id).await.map_err(|e| e.to_string())
}

/// Search SMS messages
#[tauri::command]
pub async fn search_sms(
    search_term: String,
    limit: u32,
    state: State<'_, AppState>,
) -> Result<Vec<SmsMessage>, String> {
    let database = state.database.lock().await;
    database
        .search_sms(&search_term, limit)
        .await
        .map_err(|e| e.to_string())
}

/// Get signal strength
#[tauri::command]
pub async fn get_signal_strength(state: State<'_, AppState>) -> Result<i32, String> {
    let serial_manager = state.serial_manager.lock().await;
    if !serial_manager.is_connected() {
        return Ok(0);
    }

    serial_manager
        .get_signal_strength()
        .await
        .map_err(|e| e.to_string())
}

/// Get network status
#[tauri::command]
pub async fn get_network_status(state: State<'_, AppState>) -> Result<String, String> {
    let serial_manager = state.serial_manager.lock().await;
    if !serial_manager.is_connected() {
        return Ok("Not connected".to_string());
    }

    serial_manager
        .get_network_status()
        .await
        .map_err(|e| e.to_string())
}

/// Check for new SMS messages
#[tauri::command]
pub async fn check_new_sms(state: State<'_, AppState>) -> Result<Vec<SmsMessage>, String> {
    let serial_manager = state.serial_manager.lock().await;
    let mut sms_manager = state.sms_manager.lock().await;
    let database = state.database.lock().await;

    if !serial_manager.is_connected() {
        return Ok(Vec::new());
    }

    // Read new SMS PDUs
    let pdus = serial_manager
        .read_new_sms()
        .await
        .map_err(|e| e.to_string())?;

    let mut new_messages = Vec::new();

    for pdu in pdus {
        if let Ok(Some(mut sms)) = sms_manager.process_received_sms(&pdu).await {
            // Save to database
            if let Ok(id) = database.save_sms(&sms).await {
                sms.id = Some(id);
                new_messages.push(sms);
            }
        }
    }

    Ok(new_messages)
}

/// Load all SMS from SIM card and save to database (like original C# code)
#[tauri::command]
pub async fn load_sms_from_sim(state: State<'_, AppState>) -> Result<String, String> {
    let serial_manager = state.serial_manager.lock().await;
    if !serial_manager.is_connected() {
        return Err("Not connected to GSM modem".to_string());
    }

    info!("Loading SMS messages from SIM card...");

    // First, set SMS format to text mode
    let response = serial_manager.send_at_command("AT+CMGF=1").await.map_err(|e| e.to_string())?;
    if !response.contains("OK") {
        return Err("Failed to set SMS text mode".to_string());
    }

    // List all SMS messages
    let response = serial_manager.send_at_command("AT+CMGL=\"ALL\"").await.map_err(|e| e.to_string())?;

    let mut loaded_count = 0;
    let lines: Vec<&str> = response.lines().collect();
    let mut i = 0;

    while i < lines.len() {
        let line = lines[i].trim();

        // Look for SMS header line (+CMGL: ...)
        if line.starts_with("+CMGL:") {
            // Parse SMS header
            if let Some(sms_data) = parse_sms_header(line) {
                // Next line should contain the message content
                if i + 1 < lines.len() {
                    let message_content = lines[i + 1].trim();

                    // Create SMS message object
                    let sms_message = SmsMessage {
                        id: None,
                        phone_no: sms_data.phone_no,
                        phone_to: "".to_string(), // Will be filled based on message type
                        create_date: sms_data.timestamp,
                        message: message_content.to_string(),
                        message_type: sms_data.message_type,
                        batch_no: 0,
                        order_no: 0,
                    };

                    // Save to database
                    let database = state.database.lock().await;
                    match database.save_sms(&sms_message).await {
                        Ok(_) => {
                            loaded_count += 1;
                            debug!("Saved SMS: {}", message_content);
                        }
                        Err(e) => {
                            warn!("Failed to save SMS: {}", e);
                        }
                    }

                    i += 2; // Skip both header and content lines
                } else {
                    i += 1;
                }
            } else {
                i += 1;
            }
        } else {
            i += 1;
        }
    }

    info!("Loaded {} SMS messages from SIM card", loaded_count);
    Ok(format!("Successfully loaded {} SMS messages from SIM card", loaded_count))
}

/// Parse SMS header line from AT+CMGL response
fn parse_sms_header(header_line: &str) -> Option<SmsData> {
    // Example: +CMGL: 1,"REC READ","+1234567890",,"21/03/15,10:30:45+32"
    let parts: Vec<&str> = header_line.split(',').collect();
    if parts.len() >= 5 {
        let status = parts[1].trim_matches('"');
        let phone_no = parts[2].trim_matches('"').to_string();
        let timestamp_str = parts[4].trim_matches('"');

        // Determine message type based on status
        let message_type = if status.contains("REC") {
            SmsType::Received
        } else {
            SmsType::Sent
        };

        // Parse timestamp (format: "yy/MM/dd,HH:mm:ss+tz")
        let timestamp = parse_sms_timestamp(timestamp_str).unwrap_or_else(|| {
            chrono::Utc::now().format("%Y-%m-%d %H:%M:%S").to_string()
        });

        Some(SmsData {
            phone_no,
            timestamp,
            message_type,
        })
    } else {
        None
    }
}

/// Parse SMS timestamp from GSM format
fn parse_sms_timestamp(timestamp_str: &str) -> Option<String> {
    // Input format: "21/03/15,10:30:45+32"
    // Output format: "2021-03-15 10:30:45"

    if let Some(datetime_part) = timestamp_str.split('+').next() {
        let parts: Vec<&str> = datetime_part.split(',').collect();
        if parts.len() == 2 {
            let date_part = parts[0]; // "21/03/15"
            let time_part = parts[1]; // "10:30:45"

            let date_parts: Vec<&str> = date_part.split('/').collect();
            if date_parts.len() == 3 {
                let year = format!("20{}", date_parts[0]); // Assume 20xx
                let month = date_parts[1];
                let day = date_parts[2];

                return Some(format!("{}-{}-{} {}", year, month, day, time_part));
            }
        }
    }

    None
}

/// Helper struct for SMS data parsing
struct SmsData {
    phone_no: String,
    timestamp: String,
    message_type: SmsType,
}
