use crate::core::types::{SerialPortInfo, SmsMessage, SmsType};
use crate::gui::app_state::AppState;
use serde::{Deserialize, Serialize};
use tauri::State;

#[derive(Debug, Serialize, Deserialize)]
pub struct SendSmsRequest {
    pub phone_number: String,
    pub message: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SmsHistoryResponse {
    pub messages: Vec<SmsMessage>,
    pub total_count: u32,
}

/// Get available serial ports
#[tauri::command]
pub async fn get_serial_ports() -> Result<Vec<SerialPortInfo>, String> {
    crate::core::serial_manager::SerialManager::get_available_ports()
        .await
        .map_err(|e| e.to_string())
}

/// Connect to serial port
#[tauri::command]
pub async fn connect_serial(
    port_name: String,
    state: State<'_, AppState>,
) -> Result<(), String> {
    let mut serial_manager = state.serial_manager.lock().await;

    // Clear any existing connection
    if serial_manager.is_connected() {
        serial_manager.disconnect().await.map_err(|e| e.to_string())?;
    }

    // Connect to the port
    serial_manager.connect(&port_name).await.map_err(|e| e.to_string())?;

    // Clear buffer and initialize modem after connection
    serial_manager.clear_buffer().await.map_err(|e| e.to_string())?;
    serial_manager.initialize_modem().await.map_err(|e| e.to_string())?;

    Ok(())
}

/// Disconnect from serial port
#[tauri::command]
pub async fn disconnect_serial(state: State<'_, AppState>) -> Result<(), String> {
    let mut serial_manager = state.serial_manager.lock().await;
    serial_manager.disconnect().await.map_err(|e| e.to_string())
}

/// Get connection status
#[tauri::command]
pub async fn get_connection_status(state: State<'_, AppState>) -> Result<bool, String> {
    let serial_manager = state.serial_manager.lock().await;
    Ok(serial_manager.is_connected())
}

/// Test connection and get diagnostics
#[tauri::command]
pub async fn test_connection(state: State<'_, AppState>) -> Result<String, String> {
    let serial_manager = state.serial_manager.lock().await;
    if !serial_manager.is_connected() {
        return Ok("Not connected to any serial port".to_string());
    }

    serial_manager.test_connection().await.map_err(|e| e.to_string())
}

/// Send SMS message
#[tauri::command]
pub async fn send_sms(
    request: SendSmsRequest,
    state: State<'_, AppState>,
) -> Result<(), String> {
    let serial_manager = state.serial_manager.lock().await;
    let sms_manager = state.sms_manager.lock().await;
    let database = state.database.lock().await;

    // Send SMS
    let _results = sms_manager
        .send_sms(&*serial_manager, &request.phone_number, &request.message)
        .await
        .map_err(|e| e.to_string())?;

    // Save to database
    let sms_message = SmsMessage {
        id: None,
        phone_no: String::new(), // Current user's number (unknown)
        phone_to: request.phone_number,
        message: request.message,
        create_date: std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs()
            .to_string(),
        message_type: SmsType::Sent,
        batch_no: 0,
        order_no: 0,
    };

    database.save_sms(&sms_message).await.map_err(|e| e.to_string())?;

    Ok(())
}

/// Get SMS history
#[tauri::command]
pub async fn get_sms_history(
    limit: u32,
    offset: u32,
    state: State<'_, AppState>,
) -> Result<SmsHistoryResponse, String> {
    let database = state.database.lock().await;

    let messages = database
        .get_sms_history(limit, offset)
        .await
        .map_err(|e| e.to_string())?;

    // For now, return the count of retrieved messages as total
    // In a real implementation, you'd want a separate count query
    let total_count = messages.len() as u32;

    Ok(SmsHistoryResponse {
        messages,
        total_count,
    })
}

/// Delete SMS message
#[tauri::command]
pub async fn delete_sms(id: i64, state: State<'_, AppState>) -> Result<bool, String> {
    let database = state.database.lock().await;
    database.delete_sms(id).await.map_err(|e| e.to_string())
}

/// Search SMS messages
#[tauri::command]
pub async fn search_sms(
    search_term: String,
    limit: u32,
    state: State<'_, AppState>,
) -> Result<Vec<SmsMessage>, String> {
    let database = state.database.lock().await;
    database
        .search_sms(&search_term, limit)
        .await
        .map_err(|e| e.to_string())
}

/// Get signal strength
#[tauri::command]
pub async fn get_signal_strength(state: State<'_, AppState>) -> Result<i32, String> {
    let serial_manager = state.serial_manager.lock().await;
    if !serial_manager.is_connected() {
        return Ok(0);
    }

    serial_manager
        .get_signal_strength()
        .await
        .map_err(|e| e.to_string())
}

/// Get network status
#[tauri::command]
pub async fn get_network_status(state: State<'_, AppState>) -> Result<String, String> {
    let serial_manager = state.serial_manager.lock().await;
    if !serial_manager.is_connected() {
        return Ok("Not connected".to_string());
    }

    serial_manager
        .get_network_status()
        .await
        .map_err(|e| e.to_string())
}

/// Check for new SMS messages
#[tauri::command]
pub async fn check_new_sms(state: State<'_, AppState>) -> Result<Vec<SmsMessage>, String> {
    let serial_manager = state.serial_manager.lock().await;
    let mut sms_manager = state.sms_manager.lock().await;
    let database = state.database.lock().await;

    if !serial_manager.is_connected() {
        return Ok(Vec::new());
    }

    // Read new SMS PDUs
    let pdus = serial_manager
        .read_new_sms()
        .await
        .map_err(|e| e.to_string())?;

    let mut new_messages = Vec::new();

    for pdu in pdus {
        if let Ok(Some(mut sms)) = sms_manager.process_received_sms(&pdu).await {
            // Save to database
            if let Ok(id) = database.save_sms(&sms).await {
                sms.id = Some(id);
                new_messages.push(sms);
            }
        }
    }

    Ok(new_messages)
}
