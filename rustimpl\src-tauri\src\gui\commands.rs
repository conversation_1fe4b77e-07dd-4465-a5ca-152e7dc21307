use crate::core::serial_manager::SerialManager;
use crate::core::types::{SerialPortInfo, SmsMessage, SmsType};
use crate::gui::app_state::AppState;
use log::{debug, error, info, warn};
use serde::{Deserialize, Serialize};
use tauri::State;

#[derive(Debug, Serialize, Deserialize)]
pub struct SendSmsRequest {
    pub phone_number: String,
    pub message: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SmsHistoryResponse {
    pub messages: Vec<SmsMessage>,
    pub total_count: u32,
}

/// Get available serial ports
#[tauri::command]
pub async fn get_serial_ports() -> Result<Vec<SerialPortInfo>, String> {
    crate::core::serial_manager::SerialManager::get_available_ports()
        .await
        .map_err(|e| e.to_string())
}

/// Connect to serial port
#[tauri::command]
pub async fn connect_serial(
    port_name: String,
    state: State<'_, AppState>,
) -> Result<(), String> {
    let mut serial_manager = state.serial_manager.lock().await;

    // Clear any existing connection
    if serial_manager.is_connected() {
        serial_manager.disconnect().await.map_err(|e| e.to_string())?;
    }

    // Connect to the port
    serial_manager.connect(&port_name).await.map_err(|e| e.to_string())?;

    // Clear buffer and initialize modem after connection
    serial_manager.clear_buffer().await.map_err(|e| e.to_string())?;
    serial_manager.initialize_modem().await.map_err(|e| e.to_string())?;

    Ok(())
}

/// Disconnect from serial port
#[tauri::command]
pub async fn disconnect_serial(state: State<'_, AppState>) -> Result<(), String> {
    let mut serial_manager = state.serial_manager.lock().await;
    serial_manager.disconnect().await.map_err(|e| e.to_string())
}

/// Get connection status
#[tauri::command]
pub async fn get_connection_status(state: State<'_, AppState>) -> Result<bool, String> {
    let serial_manager = state.serial_manager.lock().await;
    Ok(serial_manager.is_connected())
}

/// Test connection and get diagnostics
#[tauri::command]
pub async fn test_connection(state: State<'_, AppState>) -> Result<String, String> {
    let serial_manager = state.serial_manager.lock().await;
    if !serial_manager.is_connected() {
        return Ok("Not connected to any serial port".to_string());
    }

    serial_manager.test_connection().await.map_err(|e| e.to_string())
}

/// Diagnose connection issues
#[tauri::command]
pub async fn diagnose_connection() -> Result<String, String> {
    let mut diagnosis = String::new();

    diagnosis.push_str("=== GSM设备连接诊断 ===\n\n");

    // Check available ports
    match SerialManager::get_available_ports().await {
        Ok(ports) => {
            diagnosis.push_str(&format!("发现 {} 个串口:\n", ports.len()));
            for port in &ports {
                let description = port.description.as_deref().unwrap_or("Unknown device");
                diagnosis.push_str(&format!("  • {} - {}\n", port.port_name, description));
            }
            diagnosis.push_str("\n");
        }
        Err(e) => {
            diagnosis.push_str(&format!("❌ 无法获取串口列表: {}\n\n", e));
        }
    }

    // Try to detect GSM devices
    diagnosis.push_str("正在检测GSM设备...\n");
    match SerialManager::auto_detect_gsm_port().await {
        Ok(Some(port)) => {
            diagnosis.push_str(&format!("✅ 检测到GSM设备: {}\n", port));
        }
        Ok(None) => {
            diagnosis.push_str("⚠️ 未检测到GSM设备\n");
        }
        Err(e) => {
            diagnosis.push_str(&format!("❌ 检测失败: {}\n", e));
        }
    }

    diagnosis.push_str("\n=== 故障排除建议 ===\n");
    diagnosis.push_str("1. 确保GSM设备已正确连接并供电\n");
    diagnosis.push_str("2. 检查设备驱动是否已安装\n");
    diagnosis.push_str("3. 确保没有其他程序占用串口\n");
    diagnosis.push_str("4. 尝试重新插拔设备\n");
    diagnosis.push_str("5. 检查SIM卡是否正确插入\n");

    Ok(diagnosis)
}

/// Auto-detect and connect to GSM modem (like original C# code)
#[tauri::command]
pub async fn auto_connect_gsm(state: State<'_, AppState>) -> Result<String, String> {
    info!("Starting auto-detection of GSM modem...");

    // First try to auto-detect GSM port
    let port_name = match SerialManager::auto_detect_gsm_port().await {
        Ok(Some(port)) => {
            info!("Auto-detected GSM modem on port: {}", port);
            port
        }
        Ok(None) => {
            return Err("No GSM modem detected. Please check device connection.".to_string());
        }
        Err(e) => {
            return Err(format!("Failed to detect GSM modem: {}", e));
        }
    };

    // Connect to the detected port
    let mut serial_manager = state.serial_manager.lock().await;

    // Clear any existing connection
    if serial_manager.is_connected() {
        if let Err(e) = serial_manager.disconnect().await {
            warn!("Failed to disconnect existing connection: {}", e);
            // Continue anyway
        }
    }

    // Connect to the auto-detected port
    serial_manager.connect(&port_name).await.map_err(|e| {
        error!("Failed to connect to port {}: {}", port_name, e);
        format!("Failed to connect to port {}: {}", port_name, e)
    })?;

    // Clear buffer and initialize modem with better error handling
    if let Err(e) = serial_manager.clear_buffer().await {
        warn!("Failed to clear buffer: {}", e);
        // Continue anyway, this is not critical
    }

    // Initialize modem with retry mechanism
    let mut init_attempts = 0;
    let max_attempts = 3;

    while init_attempts < max_attempts {
        match serial_manager.initialize_modem().await {
            Ok(_) => {
                info!("Modem initialized successfully on attempt {}", init_attempts + 1);
                break;
            }
            Err(e) => {
                init_attempts += 1;
                warn!("Modem initialization attempt {} failed: {}", init_attempts, e);

                if init_attempts >= max_attempts {
                    return Err(format!("Failed to initialize modem after {} attempts: {}", max_attempts, e));
                }

                // Wait a bit before retrying
                tokio::time::sleep(std::time::Duration::from_millis(1000)).await;
            }
        }
    }

    // Get modem info for confirmation (optional, don't fail if this doesn't work)
    let modem_info = match serial_manager.test_connection().await {
        Ok(info) => info,
        Err(e) => {
            warn!("Failed to get modem info: {}", e);
            "Modem connected but info unavailable".to_string()
        }
    };

    info!("Successfully auto-connected to GSM modem on {}", port_name);
    Ok(format!("Connected to {} successfully!\n\n{}", port_name, modem_info))
}

/// Send SMS message
#[tauri::command]
pub async fn send_sms(
    request: SendSmsRequest,
    state: State<'_, AppState>,
) -> Result<(), String> {
    let serial_manager = state.serial_manager.lock().await;
    let sms_manager = state.sms_manager.lock().await;
    let database = state.database.lock().await;

    // Send SMS
    let _results = sms_manager
        .send_sms(&*serial_manager, &request.phone_number, &request.message)
        .await
        .map_err(|e| e.to_string())?;

    // Save to database
    let sms_message = SmsMessage {
        id: None,
        phone_no: String::new(), // Current user's number (unknown)
        phone_to: request.phone_number,
        message: request.message,
        create_date: std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs()
            .to_string(),
        message_type: SmsType::Sent,
        batch_no: 0,
        order_no: 0,
    };

    database.save_sms(&sms_message).await.map_err(|e| e.to_string())?;

    Ok(())
}

/// Get SMS history
#[tauri::command]
pub async fn get_sms_history(
    limit: u32,
    offset: u32,
    state: State<'_, AppState>,
) -> Result<SmsHistoryResponse, String> {
    let database = state.database.lock().await;

    let messages = database
        .get_sms_history(limit, offset)
        .await
        .map_err(|e| e.to_string())?;

    // For now, return the count of retrieved messages as total
    // In a real implementation, you'd want a separate count query
    let total_count = messages.len() as u32;

    Ok(SmsHistoryResponse {
        messages,
        total_count,
    })
}

/// Delete SMS message
#[tauri::command]
pub async fn delete_sms(id: i64, state: State<'_, AppState>) -> Result<bool, String> {
    let database = state.database.lock().await;
    database.delete_sms(id).await.map_err(|e| e.to_string())
}

/// Search SMS messages
#[tauri::command]
pub async fn search_sms(
    search_term: String,
    limit: u32,
    state: State<'_, AppState>,
) -> Result<Vec<SmsMessage>, String> {
    let database = state.database.lock().await;
    database
        .search_sms(&search_term, limit)
        .await
        .map_err(|e| e.to_string())
}

/// Get signal strength
#[tauri::command]
pub async fn get_signal_strength(state: State<'_, AppState>) -> Result<i32, String> {
    let serial_manager = state.serial_manager.lock().await;
    if !serial_manager.is_connected() {
        return Ok(0);
    }

    serial_manager
        .get_signal_strength()
        .await
        .map_err(|e| e.to_string())
}

/// Get network status
#[tauri::command]
pub async fn get_network_status(state: State<'_, AppState>) -> Result<String, String> {
    let serial_manager = state.serial_manager.lock().await;
    if !serial_manager.is_connected() {
        return Ok("Not connected".to_string());
    }

    serial_manager
        .get_network_status()
        .await
        .map_err(|e| e.to_string())
}

/// Check for new SMS messages
#[tauri::command]
pub async fn check_new_sms(state: State<'_, AppState>) -> Result<Vec<SmsMessage>, String> {
    let serial_manager = state.serial_manager.lock().await;
    let mut sms_manager = state.sms_manager.lock().await;
    let database = state.database.lock().await;

    if !serial_manager.is_connected() {
        return Ok(Vec::new());
    }

    // Read new SMS PDUs
    let pdus = serial_manager
        .read_new_sms()
        .await
        .map_err(|e| e.to_string())?;

    let mut new_messages = Vec::new();

    for pdu in pdus {
        if let Ok(Some(mut sms)) = sms_manager.process_received_sms(&pdu).await {
            // Save to database
            if let Ok(id) = database.save_sms(&sms).await {
                sms.id = Some(id);
                new_messages.push(sms);
            }
        }
    }

    Ok(new_messages)
}

/// Load all SMS from SIM card and save to database (like original C# code)
#[tauri::command]
pub async fn load_sms_from_sim(state: State<'_, AppState>) -> Result<String, String> {
    info!("Starting SMS loading from SIM card...");

    let serial_manager = state.serial_manager.lock().await;
    if !serial_manager.is_connected() {
        error!("Attempted to load SMS but not connected to GSM modem");
        return Err("Not connected to GSM modem".to_string());
    }

    info!("Connected to GSM modem, starting SMS loading process...");

    // First, set SMS format to text mode with retry
    let mut attempts = 0;
    let max_attempts = 3;

    while attempts < max_attempts {
        match serial_manager.send_at_command("AT+CMGF=1").await {
            Ok(response) if response.contains("OK") => {
                info!("SMS text mode set successfully");
                break;
            }
            Ok(response) => {
                warn!("Unexpected response to AT+CMGF=1: {}", response);
                attempts += 1;
            }
            Err(e) => {
                warn!("Failed to set SMS text mode (attempt {}): {}", attempts + 1, e);
                attempts += 1;
            }
        }

        if attempts >= max_attempts {
            return Err("Failed to set SMS text mode after multiple attempts".to_string());
        }

        tokio::time::sleep(std::time::Duration::from_millis(500)).await;
    }

    // List all SMS messages with timeout protection
    info!("Requesting SMS list from SIM card...");
    let response = match tokio::time::timeout(
        std::time::Duration::from_secs(30), // 30 second timeout
        serial_manager.send_at_command("AT+CMGL=\"ALL\"")
    ).await {
        Ok(Ok(response)) => response,
        Ok(Err(e)) => {
            warn!("Failed to get SMS list: {}", e);
            return Err(format!("Failed to get SMS list: {}", e));
        }
        Err(_) => {
            warn!("Timeout while getting SMS list");
            return Err("Timeout while getting SMS list".to_string());
        }
    };

    info!("Parsing SMS response, got {} lines", response.lines().count());

    let mut loaded_count = 0;
    let lines: Vec<&str> = response.lines().collect();
    let mut i = 0;

    info!("Processing {} lines of SMS data", lines.len());

    while i < lines.len() {
        let line = lines[i].trim();
        debug!("Processing line {}: {}", i, line);

        // Look for SMS header line (+CMGL: ...)
        if line.starts_with("+CMGL:") {
            info!("Found SMS header at line {}: {}", i, line);

            // Parse SMS header
            match parse_sms_header(line) {
                Some(sms_data) => {
                    info!("Successfully parsed SMS header for phone: {}", sms_data.phone_no);

                    // Next line should contain the message content
                    if i + 1 < lines.len() {
                        let message_content = lines[i + 1].trim();
                        info!("SMS content: {}", message_content);

                        // Create SMS message object
                        let sms_message = SmsMessage {
                            id: None,
                            phone_no: sms_data.phone_no.clone(),
                            phone_to: "".to_string(), // Will be filled based on message type
                            create_date: sms_data.timestamp.clone(),
                            message: message_content.to_string(),
                            message_type: sms_data.message_type,
                            batch_no: 0,
                            order_no: 0,
                        };

                        // Save to database with better error handling
                        info!("Attempting to save SMS to database...");
                        match state.database.lock().await.save_sms(&sms_message).await {
                            Ok(_) => {
                                loaded_count += 1;
                                info!("Successfully saved SMS #{} from {}", loaded_count, sms_data.phone_no);
                            }
                            Err(e) => {
                                error!("Failed to save SMS from {}: {}", sms_data.phone_no, e);
                                // Continue processing other messages even if one fails
                            }
                        }

                        i += 2; // Skip both header and content lines
                    } else {
                        warn!("SMS header found but no content line at position {}", i + 1);
                        i += 1;
                    }
                }
                None => {
                    warn!("Failed to parse SMS header: {}", line);
                    i += 1;
                }
            }
        } else {
            i += 1;
        }
    }

    info!("Loaded {} SMS messages from SIM card", loaded_count);
    Ok(format!("Successfully loaded {} SMS messages from SIM card", loaded_count))
}

/// Parse SMS header line from AT+CMGL response
fn parse_sms_header(header_line: &str) -> Option<SmsData> {
    info!("Parsing SMS header: {}", header_line);

    // Example: +CMGL: 1,"REC READ","+1234567890",,"21/03/15,10:30:45+32"
    let parts: Vec<&str> = header_line.split(',').collect();
    debug!("Header parts: {:?}", parts);

    if parts.len() >= 5 {
        let status = parts[1].trim_matches('"');
        let phone_no = parts[2].trim_matches('"').to_string();
        let timestamp_str = parts[4].trim_matches('"');

        info!("Parsed - Status: {}, Phone: {}, Timestamp: {}", status, phone_no, timestamp_str);

        // Determine message type based on status
        let message_type = if status.contains("REC") {
            SmsType::Received
        } else {
            SmsType::Sent
        };

        // Parse timestamp (format: "yy/MM/dd,HH:mm:ss+tz")
        let timestamp = parse_sms_timestamp(timestamp_str).unwrap_or_else(|| {
            warn!("Failed to parse timestamp '{}', using current time", timestamp_str);
            chrono::Utc::now().format("%Y-%m-%d %H:%M:%S").to_string()
        });

        info!("Successfully parsed SMS header for {}", phone_no);
        Some(SmsData {
            phone_no,
            timestamp,
            message_type,
        })
    } else {
        error!("Invalid SMS header format, expected at least 5 parts, got {}: {}", parts.len(), header_line);
        None
    }
}

/// Parse SMS timestamp from GSM format
fn parse_sms_timestamp(timestamp_str: &str) -> Option<String> {
    // Input format: "21/03/15,10:30:45+32"
    // Output format: "2021-03-15 10:30:45"

    if let Some(datetime_part) = timestamp_str.split('+').next() {
        let parts: Vec<&str> = datetime_part.split(',').collect();
        if parts.len() == 2 {
            let date_part = parts[0]; // "21/03/15"
            let time_part = parts[1]; // "10:30:45"

            let date_parts: Vec<&str> = date_part.split('/').collect();
            if date_parts.len() == 3 {
                let year = format!("20{}", date_parts[0]); // Assume 20xx
                let month = date_parts[1];
                let day = date_parts[2];

                return Some(format!("{}-{}-{} {}", year, month, day, time_part));
            }
        }
    }

    None
}

/// Helper struct for SMS data parsing
struct SmsData {
    phone_no: String,
    timestamp: String,
    message_type: SmsType,
}
