#[cfg(target_os = "windows")]
use winreg::enums::*;
#[cfg(target_os = "windows")]
use winreg::RegKey;
use log::{debug, error, warn};

pub struct RegistryManager;

impl RegistryManager {
    pub fn new() -> Self {
        Self
    }

    /// Set application to start with Windows
    #[cfg(target_os = "windows")]
    pub fn set_startup(&self, enable: bool) -> Result<(), String> {
        let hkcu = RegKey::predef(HKEY_CURRENT_USER);
        let path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run";
        
        match hkcu.open_subkey_with_flags(path, KEY_ALL_ACCESS) {
            Ok(run_key) => {
                let app_name = "GPDSMS-Rust";
                
                if enable {
                    // Get current executable path
                    if let Ok(exe_path) = std::env::current_exe() {
                        let exe_path_str = exe_path.to_string_lossy().to_string();
                        match run_key.set_value(app_name, &exe_path_str) {
                            Ok(_) => {
                                debug!("Added application to Windows startup");
                                Ok(())
                            }
                            Err(e) => {
                                error!("Failed to add to startup: {}", e);
                                Err(format!("Failed to add to startup: {}", e))
                            }
                        }
                    } else {
                        Err("Could not determine executable path".to_string())
                    }
                } else {
                    match run_key.delete_value(app_name) {
                        Ok(_) => {
                            debug!("Removed application from Windows startup");
                            Ok(())
                        }
                        Err(e) => {
                            warn!("Failed to remove from startup (may not exist): {}", e);
                            Ok(()) // Not an error if it doesn't exist
                        }
                    }
                }
            }
            Err(e) => {
                error!("Failed to open registry key: {}", e);
                Err(format!("Failed to access registry: {}", e))
            }
        }
    }

    /// Check if application is set to start with Windows
    #[cfg(target_os = "windows")]
    pub fn is_startup_enabled(&self) -> bool {
        let hkcu = RegKey::predef(HKEY_CURRENT_USER);
        let path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run";
        
        match hkcu.open_subkey(path) {
            Ok(run_key) => {
                let app_name = "GPDSMS-Rust";
                match run_key.get_value::<String, _>(app_name) {
                    Ok(_) => true,
                    Err(_) => false,
                }
            }
            Err(_) => false,
        }
    }

    /// Non-Windows implementations
    #[cfg(not(target_os = "windows"))]
    pub fn set_startup(&self, _enable: bool) -> Result<(), String> {
        warn!("Startup management not implemented for this platform");
        Ok(())
    }

    #[cfg(not(target_os = "windows"))]
    pub fn is_startup_enabled(&self) -> bool {
        false
    }

    /// Save application settings to registry/preferences
    #[cfg(target_os = "windows")]
    pub fn save_setting(&self, key: &str, value: &str) -> Result<(), String> {
        let hkcu = RegKey::predef(HKEY_CURRENT_USER);
        let path = r"SOFTWARE\GPDSMS-Rust";
        
        match hkcu.create_subkey(path) {
            Ok((app_key, _)) => {
                match app_key.set_value(key, &value) {
                    Ok(_) => {
                        debug!("Saved setting: {} = {}", key, value);
                        Ok(())
                    }
                    Err(e) => {
                        error!("Failed to save setting: {}", e);
                        Err(format!("Failed to save setting: {}", e))
                    }
                }
            }
            Err(e) => {
                error!("Failed to create registry key: {}", e);
                Err(format!("Failed to access registry: {}", e))
            }
        }
    }

    /// Load application setting from registry/preferences
    #[cfg(target_os = "windows")]
    pub fn load_setting(&self, key: &str) -> Option<String> {
        let hkcu = RegKey::predef(HKEY_CURRENT_USER);
        let path = r"SOFTWARE\GPDSMS-Rust";
        
        match hkcu.open_subkey(path) {
            Ok(app_key) => {
                match app_key.get_value::<String, _>(key) {
                    Ok(value) => {
                        debug!("Loaded setting: {} = {}", key, value);
                        Some(value)
                    }
                    Err(_) => None,
                }
            }
            Err(_) => None,
        }
    }

    #[cfg(not(target_os = "windows"))]
    pub fn save_setting(&self, _key: &str, _value: &str) -> Result<(), String> {
        warn!("Registry settings not available on this platform");
        Ok(())
    }

    #[cfg(not(target_os = "windows"))]
    pub fn load_setting(&self, _key: &str) -> Option<String> {
        None
    }
}
