use crate::core::types::{GpdSmsError, Result, SerialPortInfo};
use log::{debug, error, info, warn};
use serialport::{SerialPort, SerialPortType};
use std::io::Write;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::Mutex;

pub struct SerialManager {
    port: Option<Arc<Mutex<Box<dyn SerialPort>>>>,
    is_connected: bool,
}

impl SerialManager {
    pub fn new() -> Self {
        Self {
            port: None,
            is_connected: false,
        }
    }

    /// Get available serial ports
    pub async fn get_available_ports() -> Result<Vec<SerialPortInfo>> {
        let ports = serialport::available_ports()?;
        let mut port_infos = Vec::new();

        for port in ports {
            let description = match &port.port_type {
                SerialPortType::UsbPort(usb_info) => {
                    format!("USB Device (VID: {:04X}, PID: {:04X})",
                           usb_info.vid, usb_info.pid)
                }
                SerialPortType::PciPort => "PCI Port".to_string(),
                SerialPortType::BluetoothPort => "Bluetooth Port".to_string(),
                SerialPortType::Unknown => "Unknown".to_string(),
            };

            port_infos.push(SerialPortInfo {
                port_name: port.port_name,
                description: Some(description),
                hardware_id: None,
            });
        }

        debug!("Found {} available serial ports", port_infos.len());
        Ok(port_infos)
    }

    /// Auto-detect GSM modem port (like original C# code)
    pub async fn auto_detect_gsm_port() -> Result<Option<String>> {
        info!("Starting GSM modem auto-detection...");

        let ports = match serialport::available_ports() {
            Ok(ports) => {
                info!("Found {} serial ports", ports.len());
                for port in &ports {
                    let description = match &port.port_type {
                        SerialPortType::UsbPort(usb_info) => {
                            format!("USB Device (VID: {:04X}, PID: {:04X})", usb_info.vid, usb_info.pid)
                        }
                        SerialPortType::PciPort => "PCI Port".to_string(),
                        SerialPortType::BluetoothPort => "Bluetooth Port".to_string(),
                        SerialPortType::Unknown => "Unknown".to_string(),
                    };
                    info!("  Port: {} - {}", port.port_name, description);
                }
                ports
            }
            Err(e) => {
                warn!("Failed to get serial ports: {}", e);
                return Err(GpdSmsError::SerialPort(e));
            }
        };

        if ports.is_empty() {
            warn!("No serial ports found on system");
            return Ok(None);
        }

        // GSM modem identifiers (based on original C# code)
        let _gsm_identifiers = vec![
            "Quectel USB AT Port",
            "USB AT Port",
            "GSM Modem",
            "USB Serial Port",
            "Prolific USB-to-Serial Comm Port",
        ];

        // Phase 1: Check by VID/PID for USB devices
        info!("Phase 1: Checking USB devices by VID/PID...");
        for port in &ports {
            let port_name = &port.port_name;

            // Check port type and description
            match &port.port_type {
                SerialPortType::UsbPort(usb_info) => {
                    // Common GSM modem VID/PID combinations
                    let is_gsm_device = match (usb_info.vid, usb_info.pid) {
                        (0x2C7C, _) => true, // Quectel
                        (0x1E0E, _) => true, // Qualcomm
                        (0x05C6, _) => true, // Qualcomm
                        (0x12D1, _) => true, // Huawei
                        (0x19D2, _) => true, // ZTE
                        _ => false,
                    };

                    if is_gsm_device {
                        info!("Found potential GSM device: {} (VID: {:04X}, PID: {:04X})",
                              port_name, usb_info.vid, usb_info.pid);

                        // Test the port to confirm it's working
                        if let Ok(Some(confirmed_port)) = Self::test_port_for_gsm(port_name).await {
                            info!("Confirmed working GSM device: {}", confirmed_port);
                            return Ok(Some(confirmed_port));
                        } else {
                            warn!("GSM device {} found by VID/PID but failed AT test", port_name);
                        }
                    }
                }
                _ => {
                    // For non-USB ports, check if it might be a GSM device by port name patterns
                    let port_name_lower = port_name.to_lowercase();
                    if port_name_lower.contains("com") {
                        info!("Found COM port, testing for GSM: {}", port_name);

                        if let Ok(Some(confirmed_port)) = Self::test_port_for_gsm(port_name).await {
                            info!("Confirmed working GSM device: {}", confirmed_port);
                            return Ok(Some(confirmed_port));
                        }
                    }
                }
            }
        }

        // Phase 2: Test all ports with AT commands
        info!("Phase 2: Testing all ports with AT commands...");
        for (index, port) in ports.iter().enumerate() {
            info!("Testing port {}/{}: {}", index + 1, ports.len(), port.port_name);

            if let Ok(Some(port_name)) = Self::test_port_for_gsm(&port.port_name).await {
                info!("Found working GSM device: {}", port_name);
                return Ok(Some(port_name));
            }
        }

        warn!("No GSM modem found after testing all ports");
        Ok(None)
    }

    /// Test a specific port to see if it responds to AT commands
    async fn test_port_for_gsm(port_name: &str) -> Result<Option<String>> {
        info!("Testing port {} for GSM modem...", port_name);

        // Try to open the port
        let port_result = serialport::new(port_name, 9600)
            .timeout(Duration::from_millis(3000))  // Increased timeout
            .data_bits(serialport::DataBits::Eight)
            .flow_control(serialport::FlowControl::None)
            .parity(serialport::Parity::None)
            .stop_bits(serialport::StopBits::One)
            .open();

        if let Ok(mut port) = port_result {
            // Clear any existing data in buffer first
            let mut discard_buffer = [0u8; 1024];
            let _ = port.read(&mut discard_buffer);

            // Wait for device to stabilize
            tokio::time::sleep(Duration::from_millis(1000)).await;

            // Try multiple AT commands with different formats
            let test_commands: Vec<&[u8]> = vec![
                b"AT\r\n",
                b"AT\r",
                b"AT\n",
                b"+++AT\r\n",  // Some modems need escape sequence
            ];

            for command in test_commands {
                if let Ok(_) = port.write_all(command) {
                    if let Ok(_) = port.flush() {
                        tokio::time::sleep(Duration::from_millis(800)).await;

                        // Try to read response multiple times
                        for _ in 0..3 {
                            let mut buffer = [0u8; 512];
                            if let Ok(bytes_read) = port.read(&mut buffer) {
                                if bytes_read > 0 {
                                    let response = String::from_utf8_lossy(&buffer[..bytes_read]);
                                    debug!("Port {} response: {:?}", port_name, response);

                                    if response.contains("OK") || response.contains("AT") {
                                        info!("Port {} responded to AT command: {}", port_name, response.trim());
                                        return Ok(Some(port_name.to_string()));
                                    }
                                }
                            }
                            tokio::time::sleep(Duration::from_millis(200)).await;
                        }
                    }
                }
            }
        }

        Ok(None)
    }

    /// Connect to serial port
    pub async fn connect(&mut self, port_name: &str) -> Result<()> {
        if self.is_connected {
            self.disconnect().await?;
        }

        let port = serialport::new(port_name, 9600)
            .timeout(Duration::from_millis(8000))  // Increased timeout to 8 seconds
            .data_bits(serialport::DataBits::Eight)
            .flow_control(serialport::FlowControl::None)
            .parity(serialport::Parity::None)
            .stop_bits(serialport::StopBits::One)
            .open()?;

        self.port = Some(Arc::new(Mutex::new(port)));
        self.is_connected = true;

        info!("Connected to serial port: {} at 9600 baud", port_name);

        // Wait longer for the connection to stabilize
        tokio::time::sleep(Duration::from_millis(2000)).await;

        // Clear any existing data in buffer
        self.clear_buffer().await?;

        // Wait a bit more after clearing buffer
        tokio::time::sleep(Duration::from_millis(500)).await;

        Ok(())
    }

    /// Disconnect from serial port
    pub async fn disconnect(&mut self) -> Result<()> {
        if let Some(_port) = self.port.take() {
            self.is_connected = false;
            info!("Disconnected from serial port");
        }
        Ok(())
    }

    /// Check if connected
    pub fn is_connected(&self) -> bool {
        self.is_connected
    }

    /// Send AT command and get response
    pub async fn send_at_command(&self, command: &str) -> Result<String> {
        if !self.is_connected {
            return Err(GpdSmsError::Other("Not connected to serial port".to_string()));
        }

        let port = self.port.as_ref()
            .ok_or_else(|| GpdSmsError::Other("Serial port not available".to_string()))?;

        let mut port_guard = port.lock().await;

        // Send command
        let command_with_cr = format!("{}\r", command);
        port_guard.write_all(command_with_cr.as_bytes())?;
        port_guard.flush()?;

        debug!("Sent AT command: {}", command);

        // Wait a moment for the command to be processed
        tokio::time::sleep(Duration::from_millis(100)).await;

        // Read response with timeout
        let response = self.read_response_with_timeout(&mut port_guard, Duration::from_secs(6)).await?;

        debug!("Received response: {}", response.trim());
        Ok(response.trim().to_string())
    }

    /// Read response from serial port with timeout
    async fn read_response_with_timeout(&self, port: &mut Box<dyn SerialPort>, timeout: Duration) -> Result<String> {
        let start_time = std::time::Instant::now();
        let mut response = String::new();
        let mut buffer = [0u8; 1024];
        let mut last_data_time = start_time;

        while start_time.elapsed() < timeout {
            match port.read(&mut buffer) {
                Ok(bytes_read) if bytes_read > 0 => {
                    let data = String::from_utf8_lossy(&buffer[..bytes_read]);
                    response.push_str(&data);
                    last_data_time = std::time::Instant::now();

                    debug!("Received data: {:?}", data.trim());

                    // Check if we have a complete response
                    if response.contains("OK") || response.contains("ERROR") || response.contains("+CME ERROR") {
                        break;
                    }

                    // Also check for common modem responses
                    if response.contains("READY") || response.contains("Call Ready") {
                        // Some modems send these on startup
                        continue;
                    }
                }
                Ok(_) => {
                    // No data available, wait a bit
                    tokio::time::sleep(Duration::from_millis(100)).await;

                    // If we have some data but no complete response, wait a bit longer
                    if !response.is_empty() && last_data_time.elapsed() > Duration::from_millis(2000) {
                        debug!("Partial response received, assuming complete: {}", response.trim());
                        break;
                    }
                }
                Err(ref e) if e.kind() == std::io::ErrorKind::TimedOut => {
                    // Timeout on read, continue waiting
                    tokio::time::sleep(Duration::from_millis(100)).await;

                    // If we have some response data, consider it valid
                    if !response.is_empty() && last_data_time.elapsed() > Duration::from_millis(1500) {
                        debug!("Timeout but have response data: {}", response.trim());
                        break;
                    }
                }
                Err(e) => {
                    error!("Error reading from serial port: {}", e);
                    // Don't fail immediately, some errors are recoverable
                    tokio::time::sleep(Duration::from_millis(100)).await;
                }
            }
        }

        // Clean up the response
        let cleaned_response = response
            .replace("\r\n", "\n")
            .replace("\r", "\n")
            .lines()
            .filter(|line| !line.trim().is_empty())
            .collect::<Vec<&str>>()
            .join("\n");

        if cleaned_response.is_empty() {
            return Err(GpdSmsError::Other("Operation timed out - no response from device".to_string()));
        }

        debug!("Final cleaned response: {}", cleaned_response);
        Ok(cleaned_response)
    }

    /// Send SMS in PDU mode
    pub async fn send_sms_pdu(&self, pdu: &str) -> Result<String> {
        // Set SMS format to PDU mode
        self.send_at_command("AT+CMGF=0").await?;

        // Calculate PDU length (excluding SMSC part)
        let pdu_length = (pdu.len() - 2) / 2;

        // Send SMS command
        let sms_command = format!("AT+CMGS={}", pdu_length);
        let response = self.send_at_command(&sms_command).await?;

        if !response.contains(">") {
            return Err(GpdSmsError::SmsSend("Failed to enter SMS input mode".to_string()));
        }

        // Send PDU data followed by Ctrl+Z
        let pdu_with_ctrl_z = format!("{}\x1A", pdu);
        let final_response = self.send_at_command(&pdu_with_ctrl_z).await?;

        if final_response.contains("OK") {
            info!("SMS sent successfully");
            Ok(final_response)
        } else {
            Err(GpdSmsError::SmsSend(format!("SMS send failed: {}", final_response)))
        }
    }

    /// Read new SMS messages
    pub async fn read_new_sms(&self) -> Result<Vec<String>> {
        // Set SMS format to PDU mode
        self.send_at_command("AT+CMGF=0").await?;

        // List unread messages
        let response = self.send_at_command("AT+CMGL=0").await?;

        let mut messages = Vec::new();
        let lines: Vec<&str> = response.lines().collect();

        for (i, line) in lines.iter().enumerate() {
            if line.starts_with("+CMGL:") {
                // Next line should contain the PDU
                if i + 1 < lines.len() {
                    let pdu = lines[i + 1].trim();
                    if !pdu.is_empty() {
                        messages.push(pdu.to_string());
                    }
                }
            }
        }

        debug!("Read {} new SMS messages", messages.len());
        Ok(messages)
    }

    /// Delete SMS message by index
    pub async fn delete_sms(&self, index: u32) -> Result<()> {
        let command = format!("AT+CMGD={}", index);
        let response = self.send_at_command(&command).await?;

        if response.contains("OK") {
            debug!("Deleted SMS at index {}", index);
            Ok(())
        } else {
            Err(GpdSmsError::Other(format!("Failed to delete SMS: {}", response)))
        }
    }

    /// Get signal strength
    pub async fn get_signal_strength(&self) -> Result<i32> {
        let response = self.send_at_command("AT+CSQ").await?;

        // Parse response like "+CSQ: 15,99"
        for line in response.lines() {
            if line.starts_with("+CSQ:") {
                let parts: Vec<&str> = line.split(':').collect();
                if parts.len() > 1 {
                    let values: Vec<&str> = parts[1].split(',').collect();
                    if let Ok(rssi) = values[0].trim().parse::<i32>() {
                        // Convert RSSI to signal strength percentage
                        let strength = if rssi == 99 {
                            0 // Unknown or not detectable
                        } else {
                            ((rssi * 100) / 31).min(100)
                        };
                        return Ok(strength);
                    }
                }
            }
        }

        Ok(0)
    }

    /// Get network registration status
    pub async fn get_network_status(&self) -> Result<String> {
        let response = self.send_at_command("AT+CREG?").await?;

        for line in response.lines() {
            if line.starts_with("+CREG:") {
                return Ok(line.to_string());
            }
        }

        Ok("Unknown".to_string())
    }

    /// Initialize modem with basic settings
    pub async fn initialize_modem(&self) -> Result<()> {
        info!("Starting modem initialization...");

        // Step 1: Wake up the modem with multiple AT commands
        info!("Step 1: Waking up modem...");
        let mut success = false;

        for attempt in 1..=5 {
            info!("Wake-up attempt {}/5", attempt);

            // Try different AT command formats
            let wake_commands = vec!["AT", "AT", "AT"];

            for cmd in wake_commands {
                match self.send_at_command(cmd).await {
                    Ok(response) if response.contains("OK") => {
                        info!("Modem woke up successfully with response: {}", response.trim());
                        success = true;
                        break;
                    }
                    Ok(response) => {
                        debug!("Wake-up response: {}", response.trim());
                    }
                    Err(e) => {
                        debug!("Wake-up command failed: {}", e);
                    }
                }
                tokio::time::sleep(Duration::from_millis(500)).await;
            }

            if success {
                break;
            }

            // Wait longer between attempts
            tokio::time::sleep(Duration::from_millis(2000)).await;
        }

        if !success {
            return Err(GpdSmsError::Other("Failed to establish basic communication with modem".to_string()));
        }

        // Step 2: Basic initialization commands (only essential ones)
        info!("Step 2: Configuring modem...");
        let init_commands = vec![
            ("ATE0", "Turn off echo", true),  // Essential
            ("AT+CMEE=1", "Enable error reporting", false),  // Optional
            ("AT+CMGF=1", "Set SMS text mode", true),  // Essential for SMS
        ];

        for (command, description, essential) in init_commands {
            debug!("Executing: {} ({})", command, description);
            match self.send_at_command(command).await {
                Ok(response) => {
                    if response.contains("OK") {
                        info!("✓ Command {} executed successfully", command);
                    } else {
                        warn!("⚠ Command {} returned unexpected response: {}", command, response);
                        if essential {
                            warn!("Essential command failed, but continuing...");
                        }
                    }
                }
                Err(e) => {
                    if essential {
                        warn!("✗ Essential command {} failed: {}", command, e);
                    } else {
                        debug!("Optional command {} failed: {}", command, e);
                    }
                }
            }

            // Longer delay between commands for stability
            tokio::time::sleep(Duration::from_millis(800)).await;
        }

        info!("Modem initialization completed successfully");
        Ok(())
    }

    /// Test connection and get modem information
    pub async fn test_connection(&self) -> Result<String> {
        if !self.is_connected {
            return Err(GpdSmsError::Other("Not connected to serial port".to_string()));
        }

        let mut info = String::new();

        // Test basic AT command
        match self.send_at_command("AT").await {
            Ok(response) => {
                if response.contains("OK") {
                    info.push_str("✓ Basic AT command: OK\n");
                } else {
                    info.push_str(&format!("⚠ Basic AT command: {}\n", response));
                }
            }
            Err(e) => {
                info.push_str(&format!("✗ Basic AT command failed: {}\n", e));
                return Ok(info);
            }
        }

        // Get modem information
        let info_commands = vec![
            ("ATI", "Modem identification"),
            ("AT+CGSN", "IMEI number"),
            ("AT+CIMI", "IMSI number"),
            ("AT+CREG?", "Network registration"),
            ("AT+CSQ", "Signal quality"),
        ];

        for (command, description) in info_commands {
            match self.send_at_command(command).await {
                Ok(response) => {
                    let clean_response = response.replace("\r", "").replace("\n", " ").trim().to_string();
                    info.push_str(&format!("✓ {}: {}\n", description, clean_response));
                }
                Err(e) => {
                    info.push_str(&format!("⚠ {} failed: {}\n", description, e));
                }
            }

            tokio::time::sleep(Duration::from_millis(100)).await;
        }

        Ok(info)
    }

    /// Clear any pending data in the serial buffer
    pub async fn clear_buffer(&self) -> Result<()> {
        if !self.is_connected {
            return Ok(());
        }

        let port = self.port.as_ref()
            .ok_or_else(|| GpdSmsError::Other("Serial port not available".to_string()))?;

        let mut port_guard = port.lock().await;
        let mut buffer = [0u8; 1024];

        // Read any pending data
        while let Ok(bytes_read) = port_guard.read(&mut buffer) {
            if bytes_read == 0 {
                break;
            }
            debug!("Cleared {} bytes from buffer", bytes_read);
        }

        Ok(())
    }
}
