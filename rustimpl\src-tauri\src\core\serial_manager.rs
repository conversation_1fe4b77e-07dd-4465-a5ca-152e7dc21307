use crate::core::types::{GpdSmsError, Result, SerialPortInfo};
use log::{debug, error, info, warn};
use serialport::{SerialPort, SerialPortType};
use std::io::Write;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::Mutex;

pub struct SerialManager {
    port: Option<Arc<Mutex<Box<dyn SerialPort>>>>,
    is_connected: bool,
}

impl SerialManager {
    pub fn new() -> Self {
        Self {
            port: None,
            is_connected: false,
        }
    }

    /// Get available serial ports
    pub async fn get_available_ports() -> Result<Vec<SerialPortInfo>> {
        let ports = serialport::available_ports()?;
        let mut port_infos = Vec::new();

        for port in ports {
            let description = match &port.port_type {
                SerialPortType::UsbPort(usb_info) => {
                    format!("USB Device (VID: {:04X}, PID: {:04X})",
                           usb_info.vid, usb_info.pid)
                }
                SerialPortType::PciPort => "PCI Port".to_string(),
                SerialPortType::BluetoothPort => "Bluetooth Port".to_string(),
                SerialPortType::Unknown => "Unknown".to_string(),
            };

            port_infos.push(SerialPortInfo {
                port_name: port.port_name,
                description: Some(description),
                hardware_id: None,
            });
        }

        debug!("Found {} available serial ports", port_infos.len());
        Ok(port_infos)
    }

    /// Auto-detect GSM modem port (like original C# code)
    pub async fn auto_detect_gsm_port() -> Result<Option<String>> {
        let ports = serialport::available_ports()?;

        // GSM modem identifiers (based on original C# code)
        let _gsm_identifiers = vec![
            "Quectel USB AT Port",
            "USB AT Port",
            "GSM Modem",
            "USB Serial Port",
            "Prolific USB-to-Serial Comm Port",
        ];

        for port in &ports {
            let port_name = &port.port_name;

            // Check port type and description
            match &port.port_type {
                SerialPortType::UsbPort(usb_info) => {
                    // Common GSM modem VID/PID combinations
                    let is_gsm_device = match (usb_info.vid, usb_info.pid) {
                        (0x2C7C, _) => true, // Quectel
                        (0x1E0E, _) => true, // Qualcomm
                        (0x05C6, _) => true, // Qualcomm
                        (0x12D1, _) => true, // Huawei
                        (0x19D2, _) => true, // ZTE
                        _ => false,
                    };

                    if is_gsm_device {
                        info!("Found potential GSM device: {} (VID: {:04X}, PID: {:04X})",
                              port_name, usb_info.vid, usb_info.pid);
                        return Ok(Some(port_name.clone()));
                    }
                }
                _ => {}
            }
        }

        // If no device found by VID/PID, try to find by testing AT commands
        info!("No GSM device found by VID/PID, trying to test ports with AT commands...");

        for port in &ports {
            if let Ok(Some(port_name)) = Self::test_port_for_gsm(&port.port_name).await {
                return Ok(Some(port_name));
            }
        }

        Ok(None)
    }

    /// Test a specific port to see if it responds to AT commands
    async fn test_port_for_gsm(port_name: &str) -> Result<Option<String>> {
        info!("Testing port {} for GSM modem...", port_name);

        // Try to open the port
        let port_result = serialport::new(port_name, 9600)
            .timeout(Duration::from_millis(2000))
            .data_bits(serialport::DataBits::Eight)
            .flow_control(serialport::FlowControl::None)
            .parity(serialport::Parity::None)
            .stop_bits(serialport::StopBits::One)
            .open();

        if let Ok(mut port) = port_result {
            // Send AT command
            if let Ok(_) = port.write_all(b"AT\r") {
                if let Ok(_) = port.flush() {
                    tokio::time::sleep(Duration::from_millis(500)).await;

                    // Try to read response
                    let mut buffer = [0u8; 256];
                    if let Ok(bytes_read) = port.read(&mut buffer) {
                        let response = String::from_utf8_lossy(&buffer[..bytes_read]);
                        if response.contains("OK") {
                            info!("Port {} responded to AT command: {}", port_name, response.trim());
                            return Ok(Some(port_name.to_string()));
                        }
                    }
                }
            }
        }

        Ok(None)
    }

    /// Connect to serial port
    pub async fn connect(&mut self, port_name: &str) -> Result<()> {
        if self.is_connected {
            self.disconnect().await?;
        }

        let port = serialport::new(port_name, 9600)
            .timeout(Duration::from_millis(5000))  // Increased timeout to 5 seconds
            .data_bits(serialport::DataBits::Eight)
            .flow_control(serialport::FlowControl::None)
            .parity(serialport::Parity::None)
            .stop_bits(serialport::StopBits::One)
            .open()?;

        self.port = Some(Arc::new(Mutex::new(port)));
        self.is_connected = true;

        info!("Connected to serial port: {} at 9600 baud", port_name);

        // Wait a moment for the connection to stabilize
        tokio::time::sleep(Duration::from_millis(500)).await;

        // Test connection with AT command
        if let Err(e) = self.send_at_command("AT").await {
            warn!("Failed to send test AT command: {}", e);
        }

        Ok(())
    }

    /// Disconnect from serial port
    pub async fn disconnect(&mut self) -> Result<()> {
        if let Some(_port) = self.port.take() {
            self.is_connected = false;
            info!("Disconnected from serial port");
        }
        Ok(())
    }

    /// Check if connected
    pub fn is_connected(&self) -> bool {
        self.is_connected
    }

    /// Send AT command and get response
    pub async fn send_at_command(&self, command: &str) -> Result<String> {
        if !self.is_connected {
            return Err(GpdSmsError::Other("Not connected to serial port".to_string()));
        }

        let port = self.port.as_ref()
            .ok_or_else(|| GpdSmsError::Other("Serial port not available".to_string()))?;

        let mut port_guard = port.lock().await;

        // Send command
        let command_with_cr = format!("{}\r", command);
        port_guard.write_all(command_with_cr.as_bytes())?;
        port_guard.flush()?;

        debug!("Sent AT command: {}", command);

        // Wait a moment for the command to be processed
        tokio::time::sleep(Duration::from_millis(100)).await;

        // Read response with timeout
        let response = self.read_response_with_timeout(&mut port_guard, Duration::from_secs(6)).await?;

        debug!("Received response: {}", response.trim());
        Ok(response.trim().to_string())
    }

    /// Read response from serial port with timeout
    async fn read_response_with_timeout(&self, port: &mut Box<dyn SerialPort>, timeout: Duration) -> Result<String> {
        let start_time = std::time::Instant::now();
        let mut response = String::new();
        let mut buffer = [0u8; 1024];

        while start_time.elapsed() < timeout {
            match port.read(&mut buffer) {
                Ok(bytes_read) if bytes_read > 0 => {
                    let data = String::from_utf8_lossy(&buffer[..bytes_read]);
                    response.push_str(&data);

                    // Check if we have a complete response
                    if response.contains("OK") || response.contains("ERROR") || response.contains("+CME ERROR") {
                        break;
                    }
                }
                Ok(_) => {
                    // No data available, wait a bit
                    tokio::time::sleep(Duration::from_millis(50)).await;
                }
                Err(ref e) if e.kind() == std::io::ErrorKind::TimedOut => {
                    // Timeout on read, continue waiting
                    tokio::time::sleep(Duration::from_millis(50)).await;
                }
                Err(e) => {
                    error!("Error reading from serial port: {}", e);
                    return Err(GpdSmsError::Io(e));
                }
            }
        }

        if response.is_empty() {
            return Err(GpdSmsError::Other("Operation timed out - no response from device".to_string()));
        }

        Ok(response)
    }

    /// Send SMS in PDU mode
    pub async fn send_sms_pdu(&self, pdu: &str) -> Result<String> {
        // Set SMS format to PDU mode
        self.send_at_command("AT+CMGF=0").await?;

        // Calculate PDU length (excluding SMSC part)
        let pdu_length = (pdu.len() - 2) / 2;

        // Send SMS command
        let sms_command = format!("AT+CMGS={}", pdu_length);
        let response = self.send_at_command(&sms_command).await?;

        if !response.contains(">") {
            return Err(GpdSmsError::SmsSend("Failed to enter SMS input mode".to_string()));
        }

        // Send PDU data followed by Ctrl+Z
        let pdu_with_ctrl_z = format!("{}\x1A", pdu);
        let final_response = self.send_at_command(&pdu_with_ctrl_z).await?;

        if final_response.contains("OK") {
            info!("SMS sent successfully");
            Ok(final_response)
        } else {
            Err(GpdSmsError::SmsSend(format!("SMS send failed: {}", final_response)))
        }
    }

    /// Read new SMS messages
    pub async fn read_new_sms(&self) -> Result<Vec<String>> {
        // Set SMS format to PDU mode
        self.send_at_command("AT+CMGF=0").await?;

        // List unread messages
        let response = self.send_at_command("AT+CMGL=0").await?;

        let mut messages = Vec::new();
        let lines: Vec<&str> = response.lines().collect();

        for (i, line) in lines.iter().enumerate() {
            if line.starts_with("+CMGL:") {
                // Next line should contain the PDU
                if i + 1 < lines.len() {
                    let pdu = lines[i + 1].trim();
                    if !pdu.is_empty() {
                        messages.push(pdu.to_string());
                    }
                }
            }
        }

        debug!("Read {} new SMS messages", messages.len());
        Ok(messages)
    }

    /// Delete SMS message by index
    pub async fn delete_sms(&self, index: u32) -> Result<()> {
        let command = format!("AT+CMGD={}", index);
        let response = self.send_at_command(&command).await?;

        if response.contains("OK") {
            debug!("Deleted SMS at index {}", index);
            Ok(())
        } else {
            Err(GpdSmsError::Other(format!("Failed to delete SMS: {}", response)))
        }
    }

    /// Get signal strength
    pub async fn get_signal_strength(&self) -> Result<i32> {
        let response = self.send_at_command("AT+CSQ").await?;

        // Parse response like "+CSQ: 15,99"
        for line in response.lines() {
            if line.starts_with("+CSQ:") {
                let parts: Vec<&str> = line.split(':').collect();
                if parts.len() > 1 {
                    let values: Vec<&str> = parts[1].split(',').collect();
                    if let Ok(rssi) = values[0].trim().parse::<i32>() {
                        // Convert RSSI to signal strength percentage
                        let strength = if rssi == 99 {
                            0 // Unknown or not detectable
                        } else {
                            ((rssi * 100) / 31).min(100)
                        };
                        return Ok(strength);
                    }
                }
            }
        }

        Ok(0)
    }

    /// Get network registration status
    pub async fn get_network_status(&self) -> Result<String> {
        let response = self.send_at_command("AT+CREG?").await?;

        for line in response.lines() {
            if line.starts_with("+CREG:") {
                return Ok(line.to_string());
            }
        }

        Ok("Unknown".to_string())
    }

    /// Initialize modem with basic settings
    pub async fn initialize_modem(&self) -> Result<()> {
        info!("Starting modem initialization...");

        // Test basic connectivity first
        let mut retry_count = 0;
        while retry_count < 3 {
            match self.send_at_command("AT").await {
                Ok(response) if response.contains("OK") => {
                    info!("Modem responded to AT command");
                    break;
                }
                Ok(response) => {
                    warn!("Unexpected response to AT command: {}", response);
                }
                Err(e) => {
                    warn!("AT command failed (attempt {}): {}", retry_count + 1, e);
                    retry_count += 1;
                    if retry_count < 3 {
                        tokio::time::sleep(Duration::from_millis(1000)).await;
                    }
                }
            }
        }

        if retry_count >= 3 {
            return Err(GpdSmsError::Other("Failed to establish basic communication with modem".to_string()));
        }

        // Basic initialization commands
        let init_commands = vec![
            ("ATE0", "Turn off echo"),
            ("AT+CMEE=1", "Enable error reporting"),
            ("AT+CNMI=2,1,0,1,0", "Set SMS notification settings"),
            ("AT+CPMS=\"SM\",\"SM\",\"SM\"", "Set SMS storage to SIM"),
            ("AT+CMGF=1", "Set SMS text mode"),
        ];

        for (command, description) in init_commands {
            debug!("Executing: {} ({})", command, description);
            match self.send_at_command(command).await {
                Ok(response) => {
                    if response.contains("OK") {
                        debug!("Command {} executed successfully", command);
                    } else {
                        warn!("Command {} returned unexpected response: {}", command, response);
                    }
                }
                Err(e) => {
                    warn!("Failed to execute init command {} ({}): {}", command, description, e);
                    // Continue with other commands even if one fails
                }
            }

            // Small delay between commands
            tokio::time::sleep(Duration::from_millis(200)).await;
        }

        info!("Modem initialization completed");
        Ok(())
    }

    /// Test connection and get modem information
    pub async fn test_connection(&self) -> Result<String> {
        if !self.is_connected {
            return Err(GpdSmsError::Other("Not connected to serial port".to_string()));
        }

        let mut info = String::new();

        // Test basic AT command
        match self.send_at_command("AT").await {
            Ok(response) => {
                if response.contains("OK") {
                    info.push_str("✓ Basic AT command: OK\n");
                } else {
                    info.push_str(&format!("⚠ Basic AT command: {}\n", response));
                }
            }
            Err(e) => {
                info.push_str(&format!("✗ Basic AT command failed: {}\n", e));
                return Ok(info);
            }
        }

        // Get modem information
        let info_commands = vec![
            ("ATI", "Modem identification"),
            ("AT+CGSN", "IMEI number"),
            ("AT+CIMI", "IMSI number"),
            ("AT+CREG?", "Network registration"),
            ("AT+CSQ", "Signal quality"),
        ];

        for (command, description) in info_commands {
            match self.send_at_command(command).await {
                Ok(response) => {
                    let clean_response = response.replace("\r", "").replace("\n", " ").trim().to_string();
                    info.push_str(&format!("✓ {}: {}\n", description, clean_response));
                }
                Err(e) => {
                    info.push_str(&format!("⚠ {} failed: {}\n", description, e));
                }
            }

            tokio::time::sleep(Duration::from_millis(100)).await;
        }

        Ok(info)
    }

    /// Clear any pending data in the serial buffer
    pub async fn clear_buffer(&self) -> Result<()> {
        if !self.is_connected {
            return Ok(());
        }

        let port = self.port.as_ref()
            .ok_or_else(|| GpdSmsError::Other("Serial port not available".to_string()))?;

        let mut port_guard = port.lock().await;
        let mut buffer = [0u8; 1024];

        // Read any pending data
        while let Ok(bytes_read) = port_guard.read(&mut buffer) {
            if bytes_read == 0 {
                break;
            }
            debug!("Cleared {} bytes from buffer", bytes_read);
        }

        Ok(())
    }
}
