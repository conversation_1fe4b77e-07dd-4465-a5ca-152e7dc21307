use crate::core::types::{GpdSms<PERSON><PERSON>r, Result, SerialPortInfo};
use log::{debug, error, info, warn};
use serialport::{SerialPort, SerialPortType};
use std::io::{<PERSON><PERSON><PERSON><PERSON>, BufReader, Write};
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::Mutex;

pub struct SerialManager {
    port: Option<Arc<Mutex<Box<dyn SerialPort>>>>,
    is_connected: bool,
}

impl SerialManager {
    pub fn new() -> Self {
        Self {
            port: None,
            is_connected: false,
        }
    }

    /// Get available serial ports
    pub async fn get_available_ports() -> Result<Vec<SerialPortInfo>> {
        let ports = serialport::available_ports()?;
        let mut port_infos = Vec::new();

        for port in ports {
            let description = match &port.port_type {
                SerialPortType::UsbPort(usb_info) => {
                    format!("USB Device (VID: {:04X}, PID: {:04X})", 
                           usb_info.vid, usb_info.pid)
                }
                SerialPortType::PciPort => "PCI Port".to_string(),
                SerialPortType::BluetoothPort => "Bluetooth Port".to_string(),
                SerialPortType::Unknown => "Unknown".to_string(),
            };

            port_infos.push(SerialPortInfo {
                port_name: port.port_name,
                description: Some(description),
                hardware_id: None,
            });
        }

        debug!("Found {} available serial ports", port_infos.len());
        Ok(port_infos)
    }

    /// Connect to serial port
    pub async fn connect(&mut self, port_name: &str) -> Result<()> {
        if self.is_connected {
            self.disconnect().await?;
        }

        let port = serialport::new(port_name, 115200)
            .timeout(Duration::from_millis(1000))
            .open()?;

        self.port = Some(Arc::new(Mutex::new(port)));
        self.is_connected = true;

        info!("Connected to serial port: {}", port_name);

        // Test connection with AT command
        if let Err(e) = self.send_at_command("AT").await {
            warn!("Failed to send test AT command: {}", e);
        }

        Ok(())
    }

    /// Disconnect from serial port
    pub async fn disconnect(&mut self) -> Result<()> {
        if let Some(_port) = self.port.take() {
            self.is_connected = false;
            info!("Disconnected from serial port");
        }
        Ok(())
    }

    /// Check if connected
    pub fn is_connected(&self) -> bool {
        self.is_connected
    }

    /// Send AT command and get response
    pub async fn send_at_command(&self, command: &str) -> Result<String> {
        if !self.is_connected {
            return Err(GpdSmsError::Other("Not connected to serial port".to_string()));
        }

        let port = self.port.as_ref()
            .ok_or_else(|| GpdSmsError::Other("Serial port not available".to_string()))?;

        let mut port_guard = port.lock().await;
        
        // Send command
        let command_with_cr = format!("{}\r", command);
        port_guard.write_all(command_with_cr.as_bytes())?;
        port_guard.flush()?;

        debug!("Sent AT command: {}", command);

        // Read response
        let mut reader = BufReader::new(&mut **port_guard);
        let mut response = String::new();
        let mut line = String::new();

        // Read until we get OK or ERROR
        loop {
            line.clear();
            match reader.read_line(&mut line) {
                Ok(0) => break, // EOF
                Ok(_) => {
                    let trimmed = line.trim();
                    if !trimmed.is_empty() && trimmed != command.trim() {
                        response.push_str(trimmed);
                        response.push('\n');
                        
                        if trimmed == "OK" || trimmed == "ERROR" || trimmed.starts_with("+CME ERROR") {
                            break;
                        }
                    }
                }
                Err(e) => {
                    error!("Error reading from serial port: {}", e);
                    break;
                }
            }
        }

        debug!("Received response: {}", response.trim());
        Ok(response.trim().to_string())
    }

    /// Send SMS in PDU mode
    pub async fn send_sms_pdu(&self, pdu: &str) -> Result<String> {
        // Set SMS format to PDU mode
        self.send_at_command("AT+CMGF=0").await?;
        
        // Calculate PDU length (excluding SMSC part)
        let pdu_length = (pdu.len() - 2) / 2;
        
        // Send SMS command
        let sms_command = format!("AT+CMGS={}", pdu_length);
        let response = self.send_at_command(&sms_command).await?;
        
        if !response.contains(">") {
            return Err(GpdSmsError::SmsSend("Failed to enter SMS input mode".to_string()));
        }
        
        // Send PDU data followed by Ctrl+Z
        let pdu_with_ctrl_z = format!("{}\x1A", pdu);
        let final_response = self.send_at_command(&pdu_with_ctrl_z).await?;
        
        if final_response.contains("OK") {
            info!("SMS sent successfully");
            Ok(final_response)
        } else {
            Err(GpdSmsError::SmsSend(format!("SMS send failed: {}", final_response)))
        }
    }

    /// Read new SMS messages
    pub async fn read_new_sms(&self) -> Result<Vec<String>> {
        // Set SMS format to PDU mode
        self.send_at_command("AT+CMGF=0").await?;
        
        // List unread messages
        let response = self.send_at_command("AT+CMGL=0").await?;
        
        let mut messages = Vec::new();
        let lines: Vec<&str> = response.lines().collect();
        
        for (i, line) in lines.iter().enumerate() {
            if line.starts_with("+CMGL:") {
                // Next line should contain the PDU
                if i + 1 < lines.len() {
                    let pdu = lines[i + 1].trim();
                    if !pdu.is_empty() {
                        messages.push(pdu.to_string());
                    }
                }
            }
        }
        
        debug!("Read {} new SMS messages", messages.len());
        Ok(messages)
    }

    /// Delete SMS message by index
    pub async fn delete_sms(&self, index: u32) -> Result<()> {
        let command = format!("AT+CMGD={}", index);
        let response = self.send_at_command(&command).await?;
        
        if response.contains("OK") {
            debug!("Deleted SMS at index {}", index);
            Ok(())
        } else {
            Err(GpdSmsError::Other(format!("Failed to delete SMS: {}", response)))
        }
    }

    /// Get signal strength
    pub async fn get_signal_strength(&self) -> Result<i32> {
        let response = self.send_at_command("AT+CSQ").await?;
        
        // Parse response like "+CSQ: 15,99"
        for line in response.lines() {
            if line.starts_with("+CSQ:") {
                let parts: Vec<&str> = line.split(':').collect();
                if parts.len() > 1 {
                    let values: Vec<&str> = parts[1].split(',').collect();
                    if let Ok(rssi) = values[0].trim().parse::<i32>() {
                        // Convert RSSI to signal strength percentage
                        let strength = if rssi == 99 {
                            0 // Unknown or not detectable
                        } else {
                            ((rssi * 100) / 31).min(100)
                        };
                        return Ok(strength);
                    }
                }
            }
        }
        
        Ok(0)
    }

    /// Get network registration status
    pub async fn get_network_status(&self) -> Result<String> {
        let response = self.send_at_command("AT+CREG?").await?;
        
        for line in response.lines() {
            if line.starts_with("+CREG:") {
                return Ok(line.to_string());
            }
        }
        
        Ok("Unknown".to_string())
    }

    /// Initialize modem with basic settings
    pub async fn initialize_modem(&self) -> Result<()> {
        // Basic initialization commands
        let init_commands = vec![
            "ATE0",        // Echo off
            "AT+CMEE=1",   // Enable error reporting
            "AT+CNMI=2,1,0,1,0", // SMS notification settings
            "AT+CPMS=\"SM\",\"SM\",\"SM\"", // Set SMS storage
        ];

        for command in init_commands {
            if let Err(e) = self.send_at_command(command).await {
                warn!("Failed to execute init command {}: {}", command, e);
            }
        }

        info!("Modem initialization completed");
        Ok(())
    }
}
