import React, { useState, useEffect } from 'react';
import { 
  Modal, 
  Tabs, 
  Form, 
  Switch, 
  InputNumber, 
  Button, 
  Space, 
  Descriptions,
  message,
  Divider,
  Typography
} from 'antd';
import { 
  GithubOutlined, 
  ReloadOutlined,
  ExportOutlined,
  ImportOutlined,
  ClearOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { useAppStore, saveSettings, loadSettings } from '../store/appStore';

const { TabPane } = Tabs;
const { Title, Text } = Typography;

interface SettingsDialogProps {
  visible: boolean;
  onClose: () => void;
}

const SettingsDialog: React.FC<SettingsDialogProps> = ({ visible, onClose }) => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('general');
  const { settings, updateSettings } = useAppStore();

  useEffect(() => {
    if (visible) {
      loadSettings();
      form.setFieldsValue(settings);
    }
  }, [visible, settings, form]);

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      saveSettings(values);
      updateSettings(values);
      message.success('设置已保存');
      onClose();
    } catch (error) {
      message.error('保存设置失败');
    }
  };

  const handleAutoStartChange = async (checked: boolean) => {
    try {
      // TODO: Call Tauri command to manage startup
      // await invoke('manage_startup', { enable: checked });
      message.success(checked ? '已启用开机自启动' : '已禁用开机自启动');
    } catch (error) {
      message.error('设置开机自启动失败: ' + error);
      form.setFieldValue('autoStart', !checked);
    }
  };

  const handleExportData = () => {
    message.info('导出功能开发中...');
  };

  const handleImportData = () => {
    message.info('导入功能开发中...');
  };

  const handleCleanupDatabase = () => {
    Modal.confirm({
      title: '确认清理',
      content: '确定要清理数据库吗？这将删除过期的短信记录。',
      onOk: () => {
        message.info('清理功能开发中...');
      },
    });
  };

  const handleClearAllData = () => {
    Modal.confirm({
      title: '危险操作',
      content: '确定要清空所有数据吗？此操作不可恢复！',
      okType: 'danger',
      onOk: () => {
        message.info('清空功能开发中...');
      },
    });
  };

  const handleCheckUpdates = () => {
    message.info('检查更新功能开发中...');
  };

  const openGithub = () => {
    // TODO: Use Tauri shell plugin to open URL
    // shell.open('https://github.com/your-username/gpdsms-rust');
    message.info('GitHub 链接功能开发中...');
  };

  return (
    <Modal
      title="设置"
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button key="save" type="primary" onClick={handleSave}>
          保存
        </Button>,
      ]}
      width={700}
      destroyOnClose
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        {/* General Settings */}
        <TabPane tab="常规设置" key="general">
          <Form form={form} layout="vertical">
            <Form.Item
              label="开机自启动"
              name="autoStart"
              valuePropName="checked"
            >
              <Switch onChange={handleAutoStartChange} />
            </Form.Item>
            <Text type="secondary" style={{ marginTop: -16, display: 'block', marginBottom: 16 }}>
              启用后，应用程序将在系统启动时自动运行
            </Text>

            <Form.Item
              label="最小化到托盘"
              name="minimizeToTray"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
            <Text type="secondary" style={{ marginTop: -16, display: 'block', marginBottom: 16 }}>
              关闭窗口时最小化到系统托盘而不是退出程序
            </Text>

            <Form.Item
              label="新消息通知"
              name="showNotifications"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
            <Text type="secondary" style={{ marginTop: -16, display: 'block', marginBottom: 16 }}>
              收到新短信时显示桌面通知
            </Text>

            <Form.Item
              label="通知声音"
              name="notificationSound"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
            <Text type="secondary" style={{ marginTop: -16, display: 'block', marginBottom: 16 }}>
              收到新短信时播放提示音
            </Text>
          </Form>
        </TabPane>

        {/* Connection Settings */}
        <TabPane tab="连接设置" key="connection">
          <Form form={form} layout="vertical">
            <Form.Item
              label="自动重连"
              name="autoReconnect"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
            <Text type="secondary" style={{ marginTop: -16, display: 'block', marginBottom: 16 }}>
              连接断开时自动尝试重新连接
            </Text>

            <Form.Item
              label="重连间隔 (秒)"
              name="reconnectInterval"
            >
              <InputNumber min={5} max={300} step={5} style={{ width: '100%' }} />
            </Form.Item>
            <Text type="secondary" style={{ marginTop: -16, display: 'block', marginBottom: 16 }}>
              自动重连的时间间隔
            </Text>

            <Form.Item
              label="连接超时 (秒)"
              name="connectionTimeout"
            >
              <InputNumber min={5} max={60} step={1} style={{ width: '100%' }} />
            </Form.Item>
            <Text type="secondary" style={{ marginTop: -16, display: 'block', marginBottom: 16 }}>
              串口连接的超时时间
            </Text>
          </Form>
        </TabPane>

        {/* Database Settings */}
        <TabPane tab="数据管理" key="database">
          <Form form={form} layout="vertical">
            <Form.Item
              label="自动清理"
              name="autoCleanup"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
            <Text type="secondary" style={{ marginTop: -16, display: 'block', marginBottom: 16 }}>
              自动清理超过指定天数的短信记录
            </Text>

            <Form.Item
              label="保留天数"
              name="retentionDays"
            >
              <InputNumber min={7} max={365} step={1} style={{ width: '100%' }} />
            </Form.Item>
            <Text type="secondary" style={{ marginTop: -16, display: 'block', marginBottom: 16 }}>
              短信记录的保留天数
            </Text>

            <Divider />
            
            <Title level={5}>数据库操作</Title>
            <Space wrap>
              <Button icon={<ExportOutlined />} onClick={handleExportData}>
                导出数据
              </Button>
              <Button icon={<ImportOutlined />} onClick={handleImportData}>
                导入数据
              </Button>
              <Button 
                icon={<ClearOutlined />} 
                onClick={handleCleanupDatabase}
                type="default"
              >
                清理数据库
              </Button>
              <Button 
                icon={<DeleteOutlined />} 
                onClick={handleClearAllData}
                danger
              >
                清空所有数据
              </Button>
            </Space>
            <Text type="secondary" style={{ marginTop: 8, display: 'block' }}>
              数据库维护和备份操作
            </Text>
          </Form>
        </TabPane>

        {/* About */}
        <TabPane tab="关于" key="about">
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <Title level={3} style={{ color: '#1890ff' }}>GPDSMS-Rust</Title>
            <Text>版本: 1.5.0</Text>
            <br />
            <Text type="secondary">基于 Rust 和 Tauri 开发的短信管理工具</Text>
          </div>

          <Divider />

          <Title level={4}>系统信息</Title>
          <Descriptions column={1} bordered size="small">
            <Descriptions.Item label="操作系统">
              {navigator.platform}
            </Descriptions.Item>
            <Descriptions.Item label="架构">
              {navigator.userAgent.includes('x64') ? 'x64' : 'x86'}
            </Descriptions.Item>
            <Descriptions.Item label="Tauri版本">
              2.0.0
            </Descriptions.Item>
            <Descriptions.Item label="React版本">
              18.2.0
            </Descriptions.Item>
          </Descriptions>

          <Divider />

          <div style={{ textAlign: 'center' }}>
            <Space>
              <Button 
                type="primary" 
                icon={<GithubOutlined />}
                onClick={openGithub}
              >
                GitHub 仓库
              </Button>
              <Button 
                icon={<ReloadOutlined />}
                onClick={handleCheckUpdates}
              >
                检查更新
              </Button>
            </Space>
          </div>
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default SettingsDialog;
