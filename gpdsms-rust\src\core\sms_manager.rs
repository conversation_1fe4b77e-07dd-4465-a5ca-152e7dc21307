use crate::core::{
    pdu_tool::PduTool,
    serial_manager::SerialManager,
    types::{GpdSmsError, Result, SmsMessage, SmsType},
};
use log::{debug, error, info, warn};
use std::collections::HashMap;

pub struct SmsManager {
    center_number: String,
    message_pages: HashMap<String, Vec<Option<String>>>,
}

impl SmsManager {
    pub fn new() -> Self {
        Self {
            center_number: String::new(),
            message_pages: HashMap::new(),
        }
    }

    /// Initialize SMS manager with center number
    pub fn initialize(&mut self, serial_manager: &mut SerialManager) -> Result<()> {
        // Get SMS center number
        let response = serial_manager.send_command("AT+CSCA?")?;
        if let Some(line) = response.lines().find(|l| l.starts_with("+CSCA:")) {
            if let Some(center_part) = line.split('"').nth(1) {
                self.center_number = center_part.to_string();
                info!("SMS center number: {}", self.center_number);
            }
        }

        // Set SMS format to PDU mode
        serial_manager.send_command("AT+CMGF=0")?;
        
        // Enable SMS notifications
        serial_manager.send_command("AT+CNMI=2,1,0,0,0")?;

        info!("SMS manager initialized");
        Ok(())
    }

    /// Send SMS message
    pub fn send_sms(
        &self,
        serial_manager: &mut SerialManager,
        phone_number: &str,
        message: &str,
    ) -> Result<()> {
        info!("Sending SMS to {}: {}", phone_number, message);

        if self.needs_pdu_encoding(message) {
            self.send_unicode_sms(serial_manager, phone_number, message)
        } else {
            self.send_text_sms(serial_manager, phone_number, message)
        }
    }

    /// Check if message needs PDU encoding (contains non-ASCII characters)
    fn needs_pdu_encoding(&self, message: &str) -> bool {
        message.chars().any(|c| c as u32 > 127)
    }

    /// Send Unicode SMS using PDU mode
    fn send_unicode_sms(
        &self,
        serial_manager: &mut SerialManager,
        phone_number: &str,
        message: &str,
    ) -> Result<()> {
        // Set PDU mode
        let response = serial_manager.send_command("AT+CMGF=0")?;
        if !response.contains("OK") {
            return Err(GpdSmsError::SmsSend("Failed to set PDU mode".to_string()));
        }

        // Set character set to UCS2
        let response = serial_manager.send_command("AT+CSCS=\"UCS2\"")?;
        if !response.contains("OK") {
            return Err(GpdSmsError::SmsSend("Failed to set UCS2 charset".to_string()));
        }

        // Encode message to PDU
        let (encoded_pdu, length) = self.encode_full_pdu(&self.center_number, phone_number, message)?;

        // Send SMS
        let cmd = format!("AT+CMGS={}", length);
        let response = serial_manager.send_command(&cmd)?;
        if !response.contains(">") {
            return Err(GpdSmsError::SmsSend("Device not ready for SMS data".to_string()));
        }

        // Send PDU data with Ctrl+Z terminator
        let pdu_cmd = format!("{}\x1A", encoded_pdu);
        let response = serial_manager.send_command(&pdu_cmd)?;
        
        if response.contains("OK") || response.contains("+CMGS:") {
            info!("SMS sent successfully");
            Ok(())
        } else {
            Err(GpdSmsError::SmsSend("Failed to send SMS".to_string()))
        }
    }

    /// Send text SMS using text mode
    fn send_text_sms(
        &self,
        serial_manager: &mut SerialManager,
        phone_number: &str,
        message: &str,
    ) -> Result<()> {
        // Set text mode
        let response = serial_manager.send_command("AT+CMGF=1")?;
        if !response.contains("OK") {
            return Err(GpdSmsError::SmsSend("Failed to set text mode".to_string()));
        }

        // Set character set to GSM
        let response = serial_manager.send_command("AT+CSCS=\"GSM\"")?;
        if !response.contains("OK") {
            return Err(GpdSmsError::SmsSend("Failed to set GSM charset".to_string()));
        }

        // Send SMS
        let cmd = format!("AT+CMGS=\"{}\"", phone_number);
        let response = serial_manager.send_command(&cmd)?;
        if !response.contains(">") {
            return Err(GpdSmsError::SmsSend("Device not ready for SMS data".to_string()));
        }

        // Send message with Ctrl+Z terminator
        let msg_cmd = format!("{}\x1A", message);
        let response = serial_manager.send_command(&msg_cmd)?;

        // Reset to PDU mode
        serial_manager.send_command("AT+CMGF=0")?;

        if response.contains("OK") || response.contains("+CMGS:") {
            info!("SMS sent successfully");
            Ok(())
        } else {
            Err(GpdSmsError::SmsSend("Failed to send SMS".to_string()))
        }
    }

    /// Process incoming SMS notification
    pub fn process_incoming_sms(
        &mut self,
        serial_manager: &mut SerialManager,
        notification: &str,
    ) -> Result<Option<SmsMessage>> {
        if notification.contains("CMTI") {
            // Extract SMS index
            if let Some(index_str) = notification.split(',').last() {
                if let Ok(index) = index_str.trim().parse::<u32>() {
                    return self.read_sms(serial_manager, index);
                }
            }
        }
        Ok(None)
    }

    /// Read SMS from device storage
    fn read_sms(&mut self, serial_manager: &mut SerialManager, index: u32) -> Result<Option<SmsMessage>> {
        let cmd = format!("AT+CMGR={}", index);
        let response = serial_manager.send_command(&cmd)?;

        for line in response.lines() {
            if line.starts_with("+CMGR:") && line.len() > 16 {
                // Find the PDU data (next line after +CMGR:)
                if let Some(pdu_line) = response.lines()
                    .skip_while(|l| !l.starts_with("+CMGR:"))
                    .nth(1) {
                    
                    let pdu_message = PduTool::decode_full_pdu(pdu_line.trim())?;
                    
                    if !pdu_message.content.is_empty() {
                        // Check if this is a multi-part message
                        if let Some(sms) = self.handle_multipart_message(&pdu_message)? {
                            return Ok(Some(sms));
                        }
                    }
                }
            }
        }

        Ok(None)
    }

    /// Handle multi-part SMS messages
    fn handle_multipart_message(&mut self, pdu_message: &crate::core::pdu_tool::PduMessage) -> Result<Option<SmsMessage>> {
        let content = &pdu_message.content;
        let phone_no = &pdu_message.phone_no;
        
        // Check if this is a multi-part message by examining Unicode bytes
        let bytes: Vec<u16> = content.encode_utf16().collect();
        
        if bytes.len() >= 3 && bytes[0] == 0x00 && bytes[1] == 0x05 {
            // Multi-part message
            let batch_no = bytes[2] as u8;
            let total_parts = bytes[5] as u8;
            let part_no = bytes[4] as u8;
            
            let key = format!("{}{}", phone_no, batch_no);
            let content_part = content.chars().skip(3).collect::<String>();
            
            // Get or create parts array
            let parts = self.message_pages.entry(key.clone())
                .or_insert_with(|| vec![None; total_parts as usize]);
            
            // Ensure parts array is correct size
            if parts.len() != total_parts as usize {
                *parts = vec![None; total_parts as usize];
            }
            
            // Store this part
            if part_no > 0 && (part_no as usize) <= parts.len() {
                parts[part_no as usize - 1] = Some(content_part);
            }
            
            // Check if all parts are received
            if parts.iter().all(|p| p.is_some()) {
                let complete_message = parts.iter()
                    .filter_map(|p| p.as_ref())
                    .cloned()
                    .collect::<String>();
                
                // Remove from pending messages
                self.message_pages.remove(&key);
                
                return Ok(Some(SmsMessage {
                    id: None,
                    phone_no: phone_no.clone(),
                    phone_to: "-".to_string(),
                    message: complete_message,
                    create_date: pdu_message.timestamp.clone(),
                    message_type: SmsType::Received,
                    batch_no: batch_no as i32,
                    order_no: part_no as i32,
                }));
            }
            
            // Not all parts received yet
            Ok(None)
        } else {
            // Single-part message
            Ok(Some(SmsMessage {
                id: None,
                phone_no: phone_no.clone(),
                phone_to: "-".to_string(),
                message: content.clone(),
                create_date: pdu_message.timestamp.clone(),
                message_type: SmsType::Received,
                batch_no: 0,
                order_no: 0,
            }))
        }
    }

    /// Encode full PDU message
    fn encode_full_pdu(&self, center_no: &str, phone_no: &str, content: &str) -> Result<(String, String)> {
        // This is a simplified implementation
        // In a real implementation, you would need to properly encode:
        // 1. SMS center number
        // 2. PDU type
        // 3. Destination address
        // 4. Protocol identifier
        // 5. Data coding scheme
        // 6. User data length
        // 7. User data (Unicode encoded)
        
        let encoded_center = self.encode_center_number(center_no);
        let encoded_phone = self.encode_phone_number(phone_no);
        let encoded_content = self.encode_unicode_content(content);
        
        let pdu = format!("{}11000D91{}000800{}", encoded_center, encoded_phone, encoded_content);
        let length = format!("{:02}", (pdu.len() - encoded_center.len()) / 2);
        
        Ok((pdu, length))
    }

    fn encode_center_number(&self, center_no: &str) -> String {
        // Simplified implementation
        format!("00") // Empty center number for now
    }

    fn encode_phone_number(&self, phone_no: &str) -> String {
        // Simplified implementation - swap digit pairs
        let mut phone = phone_no.to_string();
        if !phone.starts_with("86") {
            phone = format!("86{}", phone);
        }
        
        let mut result = String::new();
        let chars: Vec<char> = phone.chars().collect();
        
        for i in (1..chars.len()).step_by(2) {
            if i < chars.len() {
                result.push(chars[i]);
                result.push(chars[i - 1]);
            }
        }
        
        if phone.len() % 2 != 0 {
            result.push('F');
            result.push(chars[chars.len() - 1]);
        }
        
        result
    }

    fn encode_unicode_content(&self, content: &str) -> String {
        let utf16_bytes: Vec<u16> = content.encode_utf16().collect();
        let mut hex_string = String::new();
        
        for utf16_char in utf16_bytes {
            hex_string.push_str(&format!("{:04X}", utf16_char));
        }
        
        format!("{:02X}{}", hex_string.len() / 2, hex_string)
    }
}
