:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

#root {
  width: 100%;
  height: 100vh;
  margin: 0 auto;
  text-align: center;
}

/* Light theme (default) */
body {
  color: rgba(0, 0, 0, 0.87);
  background-color: #ffffff;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Ant Design customizations */
.ant-layout {
  background: #ffffff !important;
}

.ant-layout-sider {
  background: #f5f5f5 !important;
}

.ant-card {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.ant-table {
  font-size: 14px;
}

.ant-table-tbody > tr:hover > td {
  background: #f5f7fa !important;
}

.ant-btn {
  border-radius: 6px;
}

.ant-input {
  border-radius: 6px;
}

.ant-select .ant-select-selector {
  border-radius: 6px;
}

/* Message content styling */
.message-preview {
  word-break: break-word;
  line-height: 1.4;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.message-content-detail {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
  max-height: 200px;
  overflow-y: auto;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
}

/* Status indicators */
.status-connected {
  color: #52c41a;
}

.status-disconnected {
  color: #ff4d4f;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

/* Responsive design */
@media (max-width: 768px) {
  .ant-layout-sider {
    width: 250px !important;
  }
  
  .message-preview {
    max-width: 200px;
  }
}
