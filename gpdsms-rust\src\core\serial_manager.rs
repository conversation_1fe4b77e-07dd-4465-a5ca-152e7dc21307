use crate::core::types::{GpdSmsError, Result, SerialPortInfo};
use log::{debug, error, info, warn};
use serialport::{SerialPort, SerialPortType};
use std::io::{<PERSON>ufRead, BufReader, Write};
use std::sync::mpsc::{self, Receiver, Sender};
use std::thread;
use std::time::Duration;
use tokio::sync::Mutex;

pub struct SerialManager {
    port: Option<Box<dyn SerialPort>>,
    is_connected: bool,
    message_sender: Option<Sender<String>>,
    message_receiver: Option<Receiver<String>>,
}

impl SerialManager {
    pub fn new() -> Self {
        Self {
            port: None,
            is_connected: false,
            message_sender: None,
            message_receiver: None,
        }
    }

    /// Get available serial ports
    pub fn get_available_ports() -> Result<Vec<SerialPortInfo>> {
        let ports = serialport::available_ports()?;
        let mut port_info = Vec::new();

        for port in ports {
            let description = match &port.port_type {
                SerialPortType::UsbPort(usb_info) => {
                    Some(format!("USB Device (VID: {:04X}, PID: {:04X})", 
                        usb_info.vid, usb_info.pid))
                }
                SerialPortType::PciPort => Some("PCI Port".to_string()),
                SerialPortType::BluetoothPort => Some("Bluetooth Port".to_string()),
                SerialPortType::Unknown => None,
            };

            port_info.push(SerialPortInfo {
                port_name: port.port_name,
                description,
                hardware_id: None, // Could be extracted from port_type if needed
            });
        }

        Ok(port_info)
    }

    /// Connect to a serial port
    pub fn connect(&mut self, port_name: &str) -> Result<()> {
        if self.is_connected {
            self.disconnect()?;
        }

        info!("Connecting to serial port: {}", port_name);

        let port = serialport::new(port_name, 9600)
            .timeout(Duration::from_millis(1000))
            .data_bits(serialport::DataBits::Eight)
            .flow_control(serialport::FlowControl::None)
            .parity(serialport::Parity::None)
            .stop_bits(serialport::StopBits::One)
            .open()?;

        self.port = Some(port);
        self.is_connected = true;

        // Set up message channels
        let (tx, rx) = mpsc::channel();
        self.message_sender = Some(tx);
        self.message_receiver = Some(rx);

        info!("Successfully connected to {}", port_name);
        Ok(())
    }

    /// Disconnect from serial port
    pub fn disconnect(&mut self) -> Result<()> {
        if let Some(mut port) = self.port.take() {
            info!("Disconnecting from serial port");
            // Try to close gracefully
            let _ = port.write_all(b"AT+CMGD=1,4\r"); // Clear SMS storage
            thread::sleep(Duration::from_millis(100));
        }

        self.is_connected = false;
        self.message_sender = None;
        self.message_receiver = None;

        info!("Disconnected from serial port");
        Ok(())
    }

    /// Check if connected
    pub fn is_connected(&self) -> bool {
        self.is_connected
    }

    /// Send AT command and wait for response
    pub fn send_command(&mut self, command: &str) -> Result<String> {
        if !self.is_connected {
            return Err(GpdSmsError::Other("Not connected to serial port".to_string()));
        }

        let port = self.port.as_mut()
            .ok_or_else(|| GpdSmsError::Other("Serial port not available".to_string()))?;

        debug!("Sending command: {}", command);

        // Send command
        let cmd_with_cr = format!("{}\r", command);
        port.write_all(cmd_with_cr.as_bytes())?;
        port.flush()?;

        // Wait for response
        let mut response = String::new();
        let mut reader = BufReader::new(port);
        let start_time = std::time::Instant::now();
        let timeout = Duration::from_secs(6);

        while start_time.elapsed() < timeout {
            let mut line = String::new();
            match reader.read_line(&mut line) {
                Ok(0) => break, // EOF
                Ok(_) => {
                    let trimmed = line.trim();
                    if !trimmed.is_empty() {
                        response.push_str(trimmed);
                        response.push('\n');
                        
                        debug!("Received: {}", trimmed);
                        
                        if trimmed.contains("OK") || trimmed.contains("ERROR") {
                            break;
                        }
                    }
                }
                Err(ref e) if e.kind() == std::io::ErrorKind::TimedOut => {
                    thread::sleep(Duration::from_millis(10));
                    continue;
                }
                Err(e) => return Err(GpdSmsError::Io(e)),
            }
        }

        if response.is_empty() {
            warn!("No response received for command: {}", command);
            return Err(GpdSmsError::Other("No response from device".to_string()));
        }

        Ok(response)
    }

    /// Send command without waiting for response
    pub fn send_command_no_wait(&mut self, command: &str) -> Result<()> {
        if !self.is_connected {
            return Err(GpdSmsError::Other("Not connected to serial port".to_string()));
        }

        let port = self.port.as_mut()
            .ok_or_else(|| GpdSmsError::Other("Serial port not available".to_string()))?;

        debug!("Sending command (no wait): {}", command);

        let cmd_with_cr = format!("{}\r", command);
        port.write_all(cmd_with_cr.as_bytes())?;
        port.flush()?;

        thread::sleep(Duration::from_millis(100));
        Ok(())
    }

    /// Start listening for incoming messages
    pub fn start_message_listener<F>(&mut self, callback: F) -> Result<()>
    where
        F: Fn(String) + Send + 'static,
    {
        if !self.is_connected {
            return Err(GpdSmsError::Other("Not connected to serial port".to_string()));
        }

        // This would typically involve setting up a separate thread to listen
        // for incoming data and parse SMS notifications
        // For now, we'll implement a basic version

        info!("Message listener started");
        Ok(())
    }

    /// Test AT connection
    pub fn test_connection(&mut self) -> Result<bool> {
        match self.send_command("AT") {
            Ok(response) => Ok(response.contains("OK")),
            Err(_) => Ok(false),
        }
    }

    /// Get signal strength
    pub fn get_signal_strength(&mut self) -> Result<i32> {
        let response = self.send_command("AT+CSQ")?;
        
        // Parse response like "+CSQ: 15,99"
        if let Some(line) = response.lines().find(|l| l.starts_with("+CSQ:")) {
            if let Some(values) = line.split(':').nth(1) {
                if let Some(rssi_str) = values.split(',').next() {
                    if let Ok(rssi) = rssi_str.trim().parse::<i32>() {
                        return Ok(rssi);
                    }
                }
            }
        }
        
        Err(GpdSmsError::Other("Failed to parse signal strength".to_string()))
    }
}
