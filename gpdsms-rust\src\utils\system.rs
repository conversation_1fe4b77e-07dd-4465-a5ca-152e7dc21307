use log::{debug, error, info};
use std::process::Command;

pub struct SystemUtils;

impl SystemUtils {
    /// Get system information
    pub fn get_system_info() -> SystemInfo {
        SystemInfo {
            os: std::env::consts::OS.to_string(),
            arch: std::env::consts::ARCH.to_string(),
            family: std::env::consts::FAMILY.to_string(),
        }
    }

    /// Check if running as administrator (Windows)
    #[cfg(target_os = "windows")]
    pub fn is_elevated() -> bool {
        use std::ptr;
        use windows::Win32::Foundation::BOOL;
        use windows::Win32::Security::{GetTokenInformation, TokenElevation, TOKEN_ELEVATION, TOKEN_QUERY};
        use windows::Win32::System::Threading::{GetCurrentProcess, OpenProcessToken};

        unsafe {
            let mut token = windows::Win32::Foundation::HANDLE::default();
            if OpenProcessToken(GetCurrentProcess(), TOKEN_QUERY, &mut token).is_err() {
                return false;
            }

            let mut elevation = TOKEN_ELEVATION { TokenIsElevated: BOOL(0) };
            let mut size = 0u32;

            if GetTokenInformation(
                token,
                TokenElevation,
                Some(&mut elevation as *mut _ as *mut _),
                std::mem::size_of::<TOKEN_ELEVATION>() as u32,
                &mut size,
            ).is_err() {
                return false;
            }

            elevation.TokenIsElevated.as_bool()
        }
    }

    /// Check if running as administrator (non-Windows)
    #[cfg(not(target_os = "windows"))]
    pub fn is_elevated() -> bool {
        // On Unix-like systems, check if running as root
        unsafe { libc::geteuid() == 0 }
    }

    /// Create desktop shortcut (Windows)
    #[cfg(target_os = "windows")]
    pub fn create_desktop_shortcut(
        name: &str,
        target_path: &str,
        description: &str,
    ) -> Result<(), Box<dyn std::error::Error>> {
        use std::path::PathBuf;

        let desktop_path = dirs::desktop_dir()
            .ok_or("Could not find desktop directory")?;
        
        let shortcut_path = desktop_path.join(format!("{}.lnk", name));
        
        // Use PowerShell to create shortcut
        let ps_script = format!(
            r#"
            $WshShell = New-Object -comObject WScript.Shell
            $Shortcut = $WshShell.CreateShortcut("{}")
            $Shortcut.TargetPath = "{}"
            $Shortcut.Description = "{}"
            $Shortcut.Save()
            "#,
            shortcut_path.display(),
            target_path,
            description
        );

        let output = Command::new("powershell")
            .args(["-Command", &ps_script])
            .output()?;

        if output.status.success() {
            info!("Desktop shortcut created: {}", shortcut_path.display());
            Ok(())
        } else {
            let error = String::from_utf8_lossy(&output.stderr);
            error!("Failed to create desktop shortcut: {}", error);
            Err(format!("Failed to create shortcut: {}", error).into())
        }
    }

    /// Create desktop shortcut (non-Windows)
    #[cfg(not(target_os = "windows"))]
    pub fn create_desktop_shortcut(
        name: &str,
        target_path: &str,
        description: &str,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // For Linux, create a .desktop file
        let desktop_path = dirs::desktop_dir()
            .ok_or("Could not find desktop directory")?;
        
        let shortcut_path = desktop_path.join(format!("{}.desktop", name));
        
        let desktop_content = format!(
            r#"[Desktop Entry]
Version=1.0
Type=Application
Name={}
Comment={}
Exec={}
Icon={}
Terminal=false
Categories=Utility;
"#,
            name,
            description,
            target_path,
            target_path // Use executable as icon for now
        );

        std::fs::write(&shortcut_path, desktop_content)?;
        
        // Make executable
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let mut perms = std::fs::metadata(&shortcut_path)?.permissions();
            perms.set_mode(0o755);
            std::fs::set_permissions(&shortcut_path, perms)?;
        }

        info!("Desktop shortcut created: {}", shortcut_path.display());
        Ok(())
    }

    /// Get application data directory
    pub fn get_app_data_dir(app_name: &str) -> Result<std::path::PathBuf, Box<dyn std::error::Error>> {
        let app_data = dirs::data_dir()
            .ok_or("Could not find application data directory")?;
        
        let app_dir = app_data.join(app_name);
        
        if !app_dir.exists() {
            std::fs::create_dir_all(&app_dir)?;
            debug!("Created application data directory: {}", app_dir.display());
        }
        
        Ok(app_dir)
    }

    /// Get temporary directory for the application
    pub fn get_temp_dir(app_name: &str) -> Result<std::path::PathBuf, Box<dyn std::error::Error>> {
        let temp_dir = std::env::temp_dir().join(app_name);
        
        if !temp_dir.exists() {
            std::fs::create_dir_all(&temp_dir)?;
            debug!("Created temporary directory: {}", temp_dir.display());
        }
        
        Ok(temp_dir)
    }

    /// Check if another instance is running
    pub fn is_single_instance(app_name: &str) -> bool {
        // This is a simplified implementation
        // In a real application, you might want to use named mutexes or lock files
        let lock_file = std::env::temp_dir().join(format!("{}.lock", app_name));
        
        if lock_file.exists() {
            // Check if the process is still running
            if let Ok(content) = std::fs::read_to_string(&lock_file) {
                if let Ok(pid) = content.trim().parse::<u32>() {
                    // On Windows, you could check if the process is still running
                    // For now, we'll assume it's running if the file exists
                    debug!("Lock file exists with PID: {}", pid);
                    return false;
                }
            }
        }
        
        // Create lock file with current process ID
        let current_pid = std::process::id();
        if let Err(e) = std::fs::write(&lock_file, current_pid.to_string()) {
            error!("Failed to create lock file: {}", e);
        } else {
            debug!("Created lock file with PID: {}", current_pid);
        }
        
        true
    }

    /// Clean up lock file on exit
    pub fn cleanup_lock_file(app_name: &str) {
        let lock_file = std::env::temp_dir().join(format!("{}.lock", app_name));
        if lock_file.exists() {
            if let Err(e) = std::fs::remove_file(&lock_file) {
                error!("Failed to remove lock file: {}", e);
            } else {
                debug!("Removed lock file");
            }
        }
    }
}

#[derive(Debug)]
pub struct SystemInfo {
    pub os: String,
    pub arch: String,
    pub family: String,
}
