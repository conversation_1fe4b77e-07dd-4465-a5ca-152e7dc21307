# GPDSMS-Rust 架构文档

## 项目概述

GPDSMS-Rust 是原 C# Windows Forms 应用程序的 Rust 重写版本，使用 Tauri 框架构建跨平台桌面应用程序。

## 技术架构

### 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend (Vue.js)                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │   Views     │  │ Components  │  │    Stores (Pinia)   │  │
│  │             │  │             │  │                     │  │
│  │ MessageList │  │ SendSmsDialog│  │   Connection State  │  │
│  │             │  │ SettingsDialog│  │   Message State    │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
                    Tauri IPC (JSON-RPC)
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Backend (Rust)                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │    Core     │  │     GUI     │  │       Utils         │  │
│  │             │  │             │  │                     │  │
│  │ SerialMgr   │  │ Commands    │  │   Notifications     │  │
│  │ SmsMgr      │  │ AppState    │  │   Registry          │  │
│  │ Database    │  │             │  │   System            │  │
│  │ PDUTool     │  │             │  │                     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
                    System APIs (Serial, DB, OS)
                              │
┌─────────────────────────────────────────────────────────────┐
│                 Hardware & System                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │ Serial Port │  │   SQLite    │  │   Windows APIs      │  │
│  │             │  │  Database   │  │                     │  │
│  │ Quectel 4G  │  │             │  │   Registry          │  │
│  │   Module    │  │             │  │   Notifications     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### 模块详细说明

#### 1. Core 模块 (`src/core/`)

**SerialManager** (`serial_manager.rs`)
- 管理串口连接和通信
- 处理 AT 命令发送和响应
- 监听串口数据接收
- 提供连接状态管理

**SmsManager** (`sms_manager.rs`)
- 处理短信发送逻辑
- 管理多部分短信的组装
- 处理 PDU 和文本模式切换
- 监听新短信通知

**Database** (`database.rs`)
- SQLite 数据库操作
- 短信历史记录存储
- 数据查询和统计
- 数据库维护功能

**PDUTool** (`pdu_tool.rs`)
- PDU 格式编解码
- Unicode 和 GSM 7-bit 转换
- 多部分短信处理
- 时间戳解析

**Types** (`types.rs`)
- 共享数据结构定义
- 错误类型定义
- 序列化/反序列化支持

#### 2. GUI 模块 (`src/gui/`)

**Commands** (`commands.rs`)
- Tauri 命令处理函数
- 前后端通信接口
- 异步操作封装
- 错误处理和响应

**AppState** (`app_state.rs`)
- 应用程序全局状态
- 组件间共享数据
- 线程安全的状态管理

#### 3. Utils 模块 (`src/utils/`)

**Notifications** (`notifications.rs`)
- 桌面通知管理
- 跨平台通知支持
- 通知模板和样式

**Registry** (`registry.rs`)
- Windows 注册表操作
- 开机自启动管理
- 系统集成功能

**System** (`system.rs`)
- 系统信息获取
- 文件和目录操作
- 进程管理

### 前端架构 (`src/`)

#### Vue.js 组件结构

```
src/
├── App.vue                 # 主应用组件
├── main.ts                 # 应用入口
├── router/
│   └── index.ts           # 路由配置
├── views/
│   └── MessageList.vue    # 消息列表页面
├── components/
│   ├── SendSmsDialog.vue  # 发送短信对话框
│   └── SettingsDialog.vue # 设置对话框
└── stores/                # Pinia 状态管理
    └── (待实现)
```

#### 状态管理

使用 Pinia 进行状态管理：
- 连接状态
- 消息列表
- 应用设置
- 用户界面状态

## 数据流

### 1. 短信发送流程

```
用户输入 → SendSmsDialog → send_sms 命令 → SmsManager → SerialManager → 4G模块
                                                    ↓
数据库保存 ← Database ← SmsManager ← 发送确认 ← SerialManager ← 4G模块
```

### 2. 短信接收流程

```
4G模块 → SerialManager → 数据解析 → SmsManager → PDU解码 → Database保存
                                        ↓
前端更新 ← 通知发送 ← NotificationManager ← 新消息事件
```

### 3. 连接管理流程

```
用户操作 → connect_serial 命令 → SerialManager → 串口连接 → AT测试
                                        ↓
状态更新 ← 前端通知 ← 连接结果 ← SmsManager初始化
```

## 关键设计决策

### 1. 为什么选择 Tauri？

- **性能**: 比 Electron 更轻量，内存占用更少
- **安全**: Rust 的内存安全特性
- **跨平台**: 支持 Windows、Linux、macOS
- **现代化**: 使用现代 Web 技术构建 UI

### 2. 异步架构

- 使用 Tokio 异步运行时
- 非阻塞的串口通信
- 响应式的用户界面
- 并发的消息处理

### 3. 错误处理

- 统一的错误类型系统
- 优雅的错误传播
- 用户友好的错误消息
- 详细的日志记录

### 4. 数据持久化

- SQLite 嵌入式数据库
- 异步数据库操作
- 数据迁移支持
- 备份和恢复功能

## 性能优化

### 1. 内存管理

- Rust 的零成本抽象
- 智能指针和生命周期管理
- 避免不必要的数据复制
- 及时释放资源

### 2. 并发处理

- 异步 I/O 操作
- 消息队列处理
- 线程池管理
- 锁竞争最小化

### 3. 前端优化

- 虚拟滚动（大量消息）
- 组件懒加载
- 状态缓存
- 防抖和节流

## 安全考虑

### 1. 串口安全

- 输入验证和清理
- 命令注入防护
- 超时保护
- 错误恢复机制

### 2. 数据安全

- SQL 注入防护
- 数据加密（可选）
- 访问权限控制
- 审计日志

### 3. 系统安全

- 最小权限原则
- 安全的文件操作
- 注册表访问控制
- 进程隔离

## 扩展性

### 1. 插件系统

- 模块化架构
- 动态加载支持
- API 接口标准化
- 配置管理

### 2. 多设备支持

- 设备抽象层
- 驱动程序接口
- 配置文件管理
- 自动检测机制

### 3. 协议扩展

- AT 命令扩展
- 新编码格式支持
- 协议版本管理
- 向后兼容性

## 测试策略

### 1. 单元测试

- 核心逻辑测试
- 模拟串口通信
- 数据库操作测试
- PDU 编解码测试

### 2. 集成测试

- 端到端流程测试
- 组件交互测试
- 错误场景测试
- 性能基准测试

### 3. 用户界面测试

- 组件渲染测试
- 用户交互测试
- 响应式布局测试
- 跨浏览器兼容性

## 部署和分发

### 1. 构建流程

- 自动化构建脚本
- 多平台编译
- 资源打包优化
- 版本管理

### 2. 安装程序

- Windows MSI 安装包
- Linux AppImage/DEB
- macOS DMG 包
- 自动更新机制

### 3. 配置管理

- 默认配置
- 用户配置迁移
- 环境变量支持
- 命令行参数
