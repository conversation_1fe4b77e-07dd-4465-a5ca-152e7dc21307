// Test PDU encoding/decoding functionality
use std::collections::HashMap;

fn main() {
    println!("🔧 PDU 编解码功能测试");
    println!("====================");
    
    // Test 1: Unicode encoding
    println!("✅ 测试 1: Unicode 编码");
    let chinese_text = "你好世界";
    let encoded = encode_unicode(chinese_text);
    println!("   原文: {}", chinese_text);
    println!("   编码: {}", encoded);
    
    // Test 2: Unicode decoding
    println!("✅ 测试 2: Unicode 解码");
    let hex_data = "4F60597D4E16754C"; // "你好世界" in Unicode hex
    match decode_unicode(&hex_data) {
        Ok(decoded) => println!("   解码: {}", decoded),
        Err(e) => println!("   错误: {}", e),
    }
    
    // Test 3: Phone number encoding
    println!("✅ 测试 3: 电话号码编码");
    let phone = "13800138000";
    let encoded_phone = encode_phone_number(phone);
    println!("   原号码: {}", phone);
    println!("   编码: {}", encoded_phone);
    
    // Test 4: GSM 7-bit character mapping
    println!("✅ 测试 4: GSM 7-bit 字符映射");
    let gsm_chars = "Hello@World!";
    for (i, c) in gsm_chars.chars().enumerate() {
        if let Some(gsm_code) = get_gsm_7bit_code(c) {
            println!("   '{}' -> 0x{:02X}", c, gsm_code);
        }
    }
    
    // Test 5: Hex string utilities
    println!("✅ 测试 5: 十六进制工具函数");
    let test_bytes = vec![0x48, 0x65, 0x6C, 0x6C, 0x6F]; // "Hello"
    let hex_string = bytes_to_hex(&test_bytes);
    println!("   字节: {:?}", test_bytes);
    println!("   十六进制: {}", hex_string);
    
    match hex_to_bytes(&hex_string) {
        Ok(decoded_bytes) => println!("   解码回字节: {:?}", decoded_bytes),
        Err(e) => println!("   解码错误: {}", e),
    }
    
    println!("====================");
    println!("🎉 PDU 功能测试完成！");
}

fn encode_unicode(text: &str) -> String {
    text.chars()
        .map(|c| format!("{:04X}", c as u32))
        .collect::<Vec<_>>()
        .join("")
}

fn decode_unicode(hex: &str) -> Result<String, String> {
    if hex.len() % 4 != 0 {
        return Err("Invalid hex length for Unicode".to_string());
    }
    
    let mut result = String::new();
    for i in (0..hex.len()).step_by(4) {
        let hex_char = &hex[i..i+4];
        match u32::from_str_radix(hex_char, 16) {
            Ok(code_point) => {
                if let Some(c) = char::from_u32(code_point) {
                    result.push(c);
                } else {
                    return Err(format!("Invalid Unicode code point: {}", code_point));
                }
            }
            Err(_) => return Err(format!("Invalid hex: {}", hex_char)),
        }
    }
    
    Ok(result)
}

fn encode_phone_number(phone: &str) -> String {
    let mut result = String::new();
    let chars: Vec<char> = phone.chars().collect();
    
    for i in (0..chars.len()).step_by(2) {
        if i + 1 < chars.len() {
            result.push(chars[i + 1]);
            result.push(chars[i]);
        } else {
            result.push('F');
            result.push(chars[i]);
        }
    }
    
    result
}

fn get_gsm_7bit_code(c: char) -> Option<u8> {
    let mut gsm_map = HashMap::new();
    
    // Basic ASCII characters
    for i in 32..127 {
        gsm_map.insert(char::from(i), i - 32);
    }
    
    // Special GSM characters
    gsm_map.insert('@', 0x00);
    gsm_map.insert('£', 0x01);
    gsm_map.insert('$', 0x02);
    gsm_map.insert('¥', 0x03);
    
    gsm_map.get(&c).copied()
}

fn bytes_to_hex(bytes: &[u8]) -> String {
    bytes.iter()
        .map(|b| format!("{:02X}", b))
        .collect::<Vec<_>>()
        .join("")
}

fn hex_to_bytes(hex: &str) -> Result<Vec<u8>, String> {
    if hex.len() % 2 != 0 {
        return Err("Hex string length must be even".to_string());
    }
    
    let mut bytes = Vec::new();
    for i in (0..hex.len()).step_by(2) {
        let hex_byte = &hex[i..i+2];
        match u8::from_str_radix(hex_byte, 16) {
            Ok(byte) => bytes.push(byte),
            Err(_) => return Err(format!("Invalid hex byte: {}", hex_byte)),
        }
    }
    
    Ok(bytes)
}
