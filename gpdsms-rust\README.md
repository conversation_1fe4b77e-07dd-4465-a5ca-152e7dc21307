# GPDSMS-Rust

基于Rust和Tauri开发的短信管理应用程序，适用于使用Quectel 4G模块的PC端短信收发。

## 功能特性

- 🔌 串口通信管理
- 📱 短信收发功能
- 📊 PDU格式编解码
- 💾 SQLite数据库存储
- 🔔 系统通知
- 🎯 系统托盘运行
- ⚙️ 开机自启动设置

## 技术栈

### 后端 (Rust)
- **Tauri** - 跨平台桌面应用框架
- **serialport** - 串口通信
- **rusqlite** - SQLite数据库
- **tokio** - 异步运行时
- **serde** - 序列化/反序列化

### 前端 (Vue.js)
- **Vue 3** - 前端框架
- **Element Plus** - UI组件库
- **TypeScript** - 类型安全
- **Vite** - 构建工具

## 项目结构

```
gpdsms-rust/
├── src/                    # Rust后端代码
│   ├── core/              # 核心业务逻辑
│   │   ├── database.rs    # 数据库操作
│   │   ├── pdu_tool.rs    # PDU编解码
│   │   ├── serial_manager.rs # 串口管理
│   │   ├── sms_manager.rs # 短信管理
│   │   └── types.rs       # 类型定义
│   ├── gui/               # GUI相关
│   │   ├── app_state.rs   # 应用状态
│   │   └── commands.rs    # Tauri命令
│   ├── utils/             # 工具模块
│   │   ├── notifications.rs # 通知管理
│   │   ├── registry.rs    # 注册表操作
│   │   └── system.rs      # 系统工具
│   └── main.rs            # 主入口
├── src/                   # Vue前端代码
│   ├── components/        # Vue组件
│   ├── views/            # 页面视图
│   ├── stores/           # Pinia状态管理
│   └── main.ts           # 前端入口
├── Cargo.toml            # Rust依赖配置
├── package.json          # Node.js依赖配置
└── README.md             # 项目说明
```

## 开发环境设置

### 前置要求

1. **Rust** (1.70+)
   ```bash
   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
   ```

2. **Node.js** (16+)
   ```bash
   # 使用nvm安装
   nvm install 18
   nvm use 18
   ```

3. **Tauri CLI**
   ```bash
   cargo install tauri-cli
   ```

### 安装依赖

```bash
# 安装前端依赖
npm install

# Rust依赖会在构建时自动安装
```

### 开发运行

```bash
# 开发模式运行
npm run tauri dev

# 或者
cargo tauri dev
```

### 构建发布

```bash
# 构建生产版本
npm run tauri build

# 或者
cargo tauri build
```

## 使用说明

1. **连接设备**
   - 启动应用程序
   - 在侧边栏选择对应的串口
   - 点击"连接"按钮

2. **发送短信**
   - 点击"新短信"按钮
   - 输入手机号码和短信内容
   - 点击发送

3. **查看历史**
   - 在主界面查看短信历史记录
   - 支持搜索和筛选功能

4. **系统设置**
   - 点击设置按钮配置应用选项
   - 可设置开机自启动等功能

## 与原C#版本的对比

| 特性 | C# 版本 | Rust 版本 |
|------|---------|-----------|
| 性能 | 中等 | 高 |
| 内存使用 | 较高 | 低 |
| 启动速度 | 慢 | 快 |
| 可执行文件大小 | 大 | 小 |
| 跨平台支持 | Windows | Windows/Linux/macOS |
| 开发复杂度 | 低 | 中等 |

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 致谢

- 原始C#版本的开发者
- Tauri团队提供的优秀框架
- Rust社区的各种优秀crate
