use log::{debug, error};
use std::path::PathBuf;

pub struct SystemUtils;

impl SystemUtils {
    /// Get application data directory
    pub fn get_app_data_dir() -> Result<PathBuf, String> {
        match dirs::data_dir() {
            Some(mut path) => {
                path.push("GPDSMS-Rust");

                // Create directory if it doesn't exist
                if !path.exists() {
                    std::fs::create_dir_all(&path)
                        .map_err(|e| format!("Failed to create app data directory: {}", e))?;
                }

                debug!("App data directory: {:?}", path);
                Ok(path)
            }
            None => Err("Could not determine app data directory".to_string()),
        }
    }

    /// Get application config directory
    pub fn get_config_dir() -> Result<PathBuf, String> {
        match dirs::config_dir() {
            Some(mut path) => {
                path.push("GPDSMS-Rust");

                if !path.exists() {
                    std::fs::create_dir_all(&path)
                        .map_err(|e| format!("Failed to create config directory: {}", e))?;
                }

                debug!("Config directory: {:?}", path);
                Ok(path)
            }
            None => Err("Could not determine config directory".to_string()),
        }
    }

    /// Get logs directory
    pub fn get_logs_dir() -> Result<PathBuf, String> {
        let mut path = Self::get_app_data_dir()?;
        path.push("logs");

        if !path.exists() {
            std::fs::create_dir_all(&path)
                .map_err(|e| format!("Failed to create logs directory: {}", e))?;
        }

        Ok(path)
    }

    /// Get database file path
    pub fn get_database_path() -> Result<PathBuf, String> {
        let mut path = Self::get_app_data_dir()?;
        path.push("storage.db");
        Ok(path)
    }

    /// Get system information
    pub fn get_system_info() -> SystemInfo {
        SystemInfo {
            os: std::env::consts::OS.to_string(),
            arch: std::env::consts::ARCH.to_string(),
            family: std::env::consts::FAMILY.to_string(),
        }
    }

    /// Check if running as administrator/root
    #[cfg(target_os = "windows")]
    pub fn is_elevated() -> bool {
        // Simple check - try to write to a system directory
        use std::fs::OpenOptions;
        use std::io::Write;

        let test_path = r"C:\Windows\Temp\gpdsms_admin_test.tmp";
        match OpenOptions::new().create(true).write(true).open(test_path) {
            Ok(mut file) => {
                let _ = file.write_all(b"test");
                let _ = std::fs::remove_file(test_path);
                true
            }
            Err(_) => false,
        }
    }

    #[cfg(not(target_os = "windows"))]
    pub fn is_elevated() -> bool {
        // Check if running as root
        unsafe { libc::geteuid() == 0 }
    }

    /// Get available disk space for app data directory
    pub fn get_available_space() -> Result<u64, String> {
        let app_dir = Self::get_app_data_dir()?;

        #[cfg(target_os = "windows")]
        {
            use std::ffi::CString;
            use winapi::um::winnt::ULARGE_INTEGER;

            let path_cstr = CString::new(app_dir.to_string_lossy().as_bytes())
                .map_err(|e| format!("Invalid path: {}", e))?;

            let mut free_bytes: ULARGE_INTEGER = unsafe { std::mem::zeroed() };
            let mut total_bytes: ULARGE_INTEGER = unsafe { std::mem::zeroed() };

            unsafe {
                let result = winapi::um::fileapi::GetDiskFreeSpaceExA(
                    path_cstr.as_ptr(),
                    &mut free_bytes,
                    &mut total_bytes,
                    std::ptr::null_mut(),
                );

                if result != 0 {
                    Ok(*free_bytes.QuadPart() as u64)
                } else {
                    Err("Failed to get disk space".to_string())
                }
            }
        }

        #[cfg(not(target_os = "windows"))]
        {
            use std::ffi::CString;
            use std::mem;

            let path_cstr = CString::new(app_dir.to_string_lossy().as_bytes())
                .map_err(|e| format!("Invalid path: {}", e))?;

            unsafe {
                let mut stat: libc::statvfs = mem::zeroed();
                let result = libc::statvfs(path_cstr.as_ptr(), &mut stat);

                if result == 0 {
                    Ok(stat.f_bavail * stat.f_frsize)
                } else {
                    Err("Failed to get disk space".to_string())
                }
            }
        }
    }

    /// Clean up temporary files
    pub fn cleanup_temp_files() -> Result<(), String> {
        let temp_dir = std::env::temp_dir();
        let pattern = "gpdsms_";

        match std::fs::read_dir(&temp_dir) {
            Ok(entries) => {
                for entry in entries {
                    if let Ok(entry) = entry {
                        let file_name = entry.file_name();
                        if let Some(name_str) = file_name.to_str() {
                            if name_str.starts_with(pattern) {
                                if let Err(e) = std::fs::remove_file(entry.path()) {
                                    error!("Failed to remove temp file {:?}: {}", entry.path(), e);
                                } else {
                                    debug!("Removed temp file: {:?}", entry.path());
                                }
                            }
                        }
                    }
                }
                Ok(())
            }
            Err(e) => {
                error!("Failed to read temp directory: {}", e);
                Err(format!("Failed to cleanup temp files: {}", e))
            }
        }
    }
}

#[derive(Debug)]
pub struct SystemInfo {
    pub os: String,
    pub arch: String,
    pub family: String,
}
