use crate::core::types::{GpdSmsError, PduMessage, Result};
use encoding_rs::UTF_16BE;
use std::collections::HashMap;

pub struct PduTool;

impl PduTool {
    /// Decode a complete PDU message
    pub fn decode_full_pdu(pdu: &str) -> Result<PduMessage> {
        if pdu.len() > 20 && !pdu.starts_with('0') {
            return Ok(PduMessage {
                phone_no: String::new(),
                timestamp: String::new(),
                content: Self::decode_unicode(pdu)?,
            });
        }

        // Parse as complete PDU
        let mut index = 0;

        // 1. SMS Center Address (SCA)
        let sca_length = u8::from_str_radix(&pdu[index..index + 2], 16)
            .map_err(|e| GpdSmsError::PduDecode(format!("Invalid SCA length: {}", e)))?;
        
        if sca_length > 0 {
            index += (sca_length as usize) * 2 + 2;
        } else {
            index += 2;
        }

        // 2. PDU Type
        index += 2;

        // 3. Originating Address (OA)
        let phone_no_length = u8::from_str_radix(&pdu[index + 1..index + 2], 16)
            .map_err(|e| GpdSmsError::PduDecode(format!("Invalid phone number length: {}", e)))?;
        index += 2;
        
        let _phone_no_type = &pdu[index..index + 2];
        index += 2;
        
        let phone_no_hex_len = ((phone_no_length + 1) / 2 * 2) as usize;
        let phone_no = Self::decode_phone_number(&pdu[index..index + phone_no_hex_len])?;
        index += phone_no_hex_len;

        // 4. Protocol Identifier (PID)
        index += 2;

        // 5. Data Coding Scheme (DCS)
        let dcs = &pdu[index..index + 2];
        let is_unicode = dcs == "08";
        index += 2;

        // 6. Service Centre Time Stamp (SCTS)
        let timestamp = Self::decode_timestamp(&pdu[index..index + 14])?;
        index += 14;

        // 7. User Data Length (UDL)
        let ud_length = u8::from_str_radix(&pdu[index..index + 2], 16)
            .map_err(|e| GpdSmsError::PduDecode(format!("Invalid UD length: {}", e)))?;
        index += 2;

        // 8. User Data (UD)
        let content = if is_unicode {
            Self::decode_unicode(&pdu[index..])?
        } else {
            Self::decode_gsm_7bit(&pdu[index..], ud_length as usize)?
        };

        Ok(PduMessage {
            phone_no,
            timestamp,
            content,
        })
    }

    /// Decode phone number from PDU format
    fn decode_phone_number(hex: &str) -> Result<String> {
        let mut number = String::new();
        let chars: Vec<char> = hex.chars().collect();
        
        for i in (0..chars.len()).step_by(2) {
            if i + 1 >= chars.len() {
                break;
            }
            
            let pair = format!("{}{}", chars[i], chars[i + 1]);
            if pair == "FF" {
                break;
            }
            
            number.push(chars[i + 1]);
            if chars[i] != 'F' {
                number.push(chars[i]);
            }
        }
        
        // Remove leading zero if present
        if number.starts_with('0') && number.len() > 1 {
            number = number[1..].to_string();
        }
        
        Ok(number)
    }

    /// Decode timestamp from PDU format
    fn decode_timestamp(hex: &str) -> Result<String> {
        if hex.len() < 14 {
            return Err(GpdSmsError::PduDecode("Invalid timestamp length".to_string()));
        }
        
        let chars: Vec<char> = hex.chars().collect();
        
        let year = format!("{}{}", chars[1], chars[0]);
        let month = format!("{}{}", chars[3], chars[2]);
        let day = format!("{}{}", chars[5], chars[4]);
        let hour = format!("{}{}", chars[7], chars[6]);
        let minute = format!("{}{}", chars[9], chars[8]);
        let second = format!("{}{}", chars[11], chars[10]);
        
        Ok(format!("20{}-{}-{} {}:{}:{}", year, month, day, hour, minute, second))
    }

    /// Decode Unicode text from hex string
    pub fn decode_unicode(hex: &str) -> Result<String> {
        let mut bytes = Vec::new();
        let chars: Vec<char> = hex.chars().collect();
        
        for i in (0..chars.len()).step_by(4) {
            if i + 3 >= chars.len() {
                break;
            }
            
            let hex_char = format!("{}{}{}{}", chars[i], chars[i + 1], chars[i + 2], chars[i + 3]);
            let value = u16::from_str_radix(&hex_char, 16)
                .map_err(|e| GpdSmsError::PduDecode(format!("Invalid Unicode hex: {}", e)))?;
            
            bytes.extend_from_slice(&value.to_be_bytes());
        }
        
        let (decoded, _, _) = UTF_16BE.decode(&bytes);
        Ok(decoded.to_string())
    }

    /// Decode GSM 7-bit text
    fn decode_gsm_7bit(hex: &str, ud_length: usize) -> Result<String> {
        let bytes = Self::hex_to_bytes(hex)?;
        let mut septets = Vec::new();
        
        let mut bit_offset = 0;
        let mut remaining_bits = 0u8;
        let mut decoded_septet_count = 0;
        
        for byte in bytes.iter() {
            if decoded_septet_count >= ud_length {
                break;
            }
            
            let current_byte = *byte;
            let septet = ((current_byte << bit_offset) | remaining_bits) & 0x7F;
            septets.push(septet);
            decoded_septet_count += 1;
            
            if decoded_septet_count >= ud_length {
                break;
            }
            
            remaining_bits = current_byte >> (7 - bit_offset);
            bit_offset += 1;
            
            if bit_offset == 7 {
                if decoded_septet_count < ud_length {
                    septets.push(remaining_bits);
                    decoded_septet_count += 1;
                }
                bit_offset = 0;
                remaining_bits = 0;
            }
        }
        
        // Convert septets to characters
        let mut message = String::new();
        let mut is_escaped = false;
        
        for septet in septets.iter() {
            if message.len() >= ud_length {
                break;
            }
            
            if *septet == 0x1B && !is_escaped {
                is_escaped = true;
                continue;
            }
            
            let c = if is_escaped {
                Self::get_extension_char(*septet).unwrap_or(' ')
            } else {
                Self::get_default_char(*septet)
            };
            
            message.push(c);
            is_escaped = false;
        }
        
        Ok(message)
    }

    /// Convert hex string to bytes
    fn hex_to_bytes(hex: &str) -> Result<Vec<u8>> {
        let mut bytes = Vec::new();
        let chars: Vec<char> = hex.chars().collect();
        
        for i in (0..chars.len()).step_by(2) {
            if i + 1 >= chars.len() {
                break;
            }
            
            let hex_byte = format!("{}{}", chars[i], chars[i + 1]);
            let byte = u8::from_str_radix(&hex_byte, 16)
                .map_err(|e| GpdSmsError::PduDecode(format!("Invalid hex byte: {}", e)))?;
            bytes.push(byte);
        }
        
        Ok(bytes)
    }

    /// Get character from GSM 7-bit default alphabet
    fn get_default_char(septet: u8) -> char {
        const DEFAULT_ALPHABET: &[char] = &[
            '@', '£', '$', '¥', 'è', 'é', 'ù', 'ì', 'ò', 'Ç', '\n', 'Ø', 'ø', '\r', 'Å', 'å',
            'Δ', '_', 'Φ', 'Γ', 'Λ', 'Ω', 'Π', 'Ψ', 'Σ', 'Θ', 'Ξ', ' ', 'Æ', 'æ', 'ß', 'É',
            ' ', '!', '"', '#', '¤', '%', '&', '\'', '(', ')', '*', '+', ',', '-', '.', '/',
            '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', ':', ';', '<', '=', '>', '?',
            '¡', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O',
            'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'Ä', 'Ö', 'Ñ', 'Ü', '§',
            '¿', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o',
            'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'ä', 'ö', 'ñ', 'ü', 'à'
        ];
        
        if (septet as usize) < DEFAULT_ALPHABET.len() {
            DEFAULT_ALPHABET[septet as usize]
        } else {
            ' '
        }
    }

    /// Get character from GSM 7-bit extension table
    fn get_extension_char(septet: u8) -> Option<char> {
        let mut extension_table = HashMap::new();
        extension_table.insert(0x0A, '\x0C'); // Form feed
        extension_table.insert(0x14, '^');
        extension_table.insert(0x28, '{');
        extension_table.insert(0x29, '}');
        extension_table.insert(0x2F, '\\');
        extension_table.insert(0x3C, '[');
        extension_table.insert(0x3D, '~');
        extension_table.insert(0x3E, ']');
        extension_table.insert(0x40, '|');
        extension_table.insert(0x65, '€');
        
        extension_table.get(&septet).copied()
    }
}
