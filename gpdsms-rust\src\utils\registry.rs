#[cfg(target_os = "windows")]
use winreg::{enums::*, <PERSON><PERSON><PERSON>};
use log::{debug, error, info};

pub struct RegistryManager;

impl RegistryManager {
    /// Set application to start with Windows
    #[cfg(target_os = "windows")]
    pub fn set_startup(enable: bool, app_name: &str, app_path: &str) -> Result<(), Box<dyn std::error::Error>> {
        let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
        let run_key = hklm.open_subkey_with_flags(
            r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
            KEY_SET_VALUE | KEY_QUERY_VALUE,
        )?;

        if enable {
            info!("Setting {} to start with Windows", app_name);
            run_key.set_value(app_name, &app_path)?;
            debug!("Registry entry created: {} = {}", app_name, app_path);
        } else {
            info!("Removing {} from Windows startup", app_name);
            match run_key.delete_value(app_name) {
                Ok(_) => debug!("Registry entry removed: {}", app_name),
                Err(e) => {
                    // It's okay if the value doesn't exist
                    debug!("Failed to remove registry entry (may not exist): {}", e);
                }
            }
        }

        Ok(())
    }

    /// Check if application is set to start with Windows
    #[cfg(target_os = "windows")]
    pub fn is_startup_enabled(app_name: &str) -> Result<bool, Box<dyn std::error::Error>> {
        let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
        let run_key = hklm.open_subkey_with_flags(
            r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
            KEY_QUERY_VALUE,
        )?;

        match run_key.get_value::<String, _>(app_name) {
            Ok(_) => {
                debug!("Startup entry found for {}", app_name);
                Ok(true)
            }
            Err(_) => {
                debug!("No startup entry found for {}", app_name);
                Ok(false)
            }
        }
    }

    /// Get application executable path
    pub fn get_executable_path() -> Result<String, Box<dyn std::error::Error>> {
        let exe_path = std::env::current_exe()?;
        Ok(exe_path.to_string_lossy().to_string())
    }

    /// Set or remove startup (cross-platform wrapper)
    pub fn manage_startup(enable: bool, app_name: &str) -> Result<(), Box<dyn std::error::Error>> {
        #[cfg(target_os = "windows")]
        {
            let exe_path = Self::get_executable_path()?;
            Self::set_startup(enable, app_name, &exe_path)
        }

        #[cfg(not(target_os = "windows"))]
        {
            // For non-Windows platforms, you might want to implement
            // autostart using desktop files or other mechanisms
            error!("Startup management not implemented for this platform");
            Err("Startup management not supported on this platform".into())
        }
    }

    /// Check startup status (cross-platform wrapper)
    pub fn check_startup_status(app_name: &str) -> Result<bool, Box<dyn std::error::Error>> {
        #[cfg(target_os = "windows")]
        {
            Self::is_startup_enabled(app_name)
        }

        #[cfg(not(target_os = "windows"))]
        {
            error!("Startup check not implemented for this platform");
            Ok(false)
        }
    }
}
