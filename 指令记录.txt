+CMGR: "REC UNREAD","10010",,"25/05/20,03:08:20+32"
00680064006400780020FF0C75454EAB4FBF522930104E2D56FD8054901A3011

OK


+CMGR: "REC UNREAD","10010",,"25/05/20,03:49:42+32"
5C0A656C768475286237FF0C8BF756DE590D4EE54E0B7F167801529E74064E1A52A1FF1A000D0020003100300031FF1A5B9E65F68BDD8D39000D0020003100300032FF1A8D2662374F59989D000D0020003100300033FF1A4E0A67088D265355000D0020003100300034FF1A538653F28D265355000D0020003100300035FF1A79EF5206

OK



+CMGR: "REC UNREAD","10010",,"25/05/20,03:49:41+32"
00208BA9670D52A166F467096E295EA6FF014F7F75284E2D56FD8054901A004100500050FF0C8DB34E0D51FA6237529E4E1A52A130014EA48BDD8D39FF0C63075C164E0070B967E54F59989D300167E56D4191CFFF0C70B951FB002000680074007400700073003A002F002F0075002E00310030003000310030002E0063006E002F006B

OK


002B0038003600310033003000310030003100360036003500300030


0891683110305005F0240BA13110131133F10008525012000472231E5B8B4E009E23654F7EA24F608FD9752882F18BED8BF44F60660E660E60A8




/* Use ATE1 to enable echo mode */
[2025-05-21 08:20:46:389_S:] ATE1
[2025-05-21 08:20:46:394_R:] ATE1

[2025-05-21 08:20:46:400_R:] OK

/* Use text mode */
[2025-05-21 08:20:46:412_S:] AT+CMGF=1
[2025-05-21 08:20:46:414_R:] AT+CMGF=1

[2025-05-21 08:20:46:418_R:] OK

/* Use AT+CSDH=1 to show header values only in text mode */
[2025-05-21 08:20:46:426_S:] AT+CSDH=1
[2025-05-21 08:20:46:435_R:] AT+CSDH=1

[2025-05-21 08:20:46:439_R:] OK

/* Set character type */
[2025-05-21 08:20:46:449_S:] AT+CSCS="GSM"
[2025-05-21 08:20:46:456_R:] AT+CSCS="GSM"

[2025-05-21 08:20:46:459_R:] OK

/* Read the message which will change the status of the message */
[2025-05-21 08:20:46:470_S:] AT+CMGR=0
[2025-05-21 08:20:46:473_R:] AT+CMGR=0

[2025-05-21 08:20:46:518_R:] +CMGR: "REC UNREAD","10010",,"25/05/21,08:20:42+32",160,100,0,8,"+8615654160",145,16
[2025-05-21 08:20:46:518_R:] 00680064006400780020FF0C75454EAB4FBF522930104E2D56FD8054901A3011

[2025-05-21 08:20:46:518_R:] OK

/* Read the message which will change the status of the message */
[2025-05-21 08:20:50:674_S:] AT+CMGR=1
[2025-05-21 08:20:50:676_R:] AT+CMGR=1

[2025-05-21 08:20:50:720_R:] +CMGR: "REC UNREAD","10010",,"25/05/21,08:20:42+32",160,100,0,8,"+8615654160",145,66
[2025-05-21 08:20:50:720_R:] 67E58BE2000D0020003100300036FF1A79EF52066D888D398BB05F55000D0020003100300037FF1A624B673A4E0A7F516D4191CF000D0020003100350030FF1A5E38752867E58BE24E1A52A1000D0020598297008FD456DE4E3B83DC5355FF0C8BF753D19001201C00310030003000310030201D81F3003100300030003100303002000D

[2025-05-21 08:20:50:720_R:] OK

/* Read the message which will change the status of the message */
[2025-05-21 08:20:53:780_S:] AT+CMGR=2
[2025-05-21 08:20:53:789_R:] AT+CMGR=2

[2025-05-21 08:20:53:829_R:] +CMGR: "REC UNREAD","10010",,"25/05/21,08:20:42+32",160,100,0,8,"+8615654160",145,66
[2025-05-21 08:20:53:829_R:] 00208BA9670D52A166F467096E295EA6FF014F7F75284E2D56FD8054901A004100500050FF0C8DB34E0D51FA6237529E4E1A52A130014EA48BDD8D39FF0C63075C164E0070B967E54F59989D300167E56D4191CFFF0C70B951FB002000680074007400700073003A002F002F0075002E00310030003000310030002E0063006E002F006B

[2025-05-21 08:20:53:829_R:] OK

/* Use AT+CSDH=0 to hide header values only in text mode */
[2025-05-21 08:20:56:094_S:] AT+CSDH=0
[2025-05-21 08:20:56:104_R:] AT+CSDH=0

[2025-05-21 08:20:56:108_R:] OK

/* Use ATE1 to enable echo mode */
[2025-05-21 08:20:58:378_S:] ATE1
[2025-05-21 08:20:58:387_R:] ATE1

[2025-05-21 08:20:58:390_R:] OK

/* Use text mode */
[2025-05-21 08:20:58:396_S:] AT+CMGF=1
[2025-05-21 08:20:58:403_R:] AT+CMGF=1

[2025-05-21 08:20:58:403_R:] OK

/* Use AT+CSDH=1 to show header values only in text mode */
[2025-05-21 08:20:58:410_S:] AT+CSDH=1
[2025-05-21 08:20:58:414_R:] AT+CSDH=1

[2025-05-21 08:20:58:414_R:] OK

/* Set character type */
[2025-05-21 08:20:58:420_S:] AT+CSCS="GSM"
[2025-05-21 08:20:58:424_R:] AT+CSCS="GSM"

[2025-05-21 08:20:58:427_R:] OK

/* Read the message which will change the status of the message */
[2025-05-21 08:20:58:439_S:] AT+CMGR=3
[2025-05-21 08:20:58:442_R:] AT+CMGR=3

[2025-05-21 08:20:58:492_R:] +CMGR: "REC UNREAD","10010",,"25/05/21,08:20:42+32",160,100,0,8,"+8615654160",145,66
[2025-05-21 08:20:58:492_R:] 5C0A656C768475286237FF0C8BF756DE590D4EE54E0B7F167801529E74064E1A52A1FF1A000D0020003100300031FF1A5B9E65F68BDD8D39000D0020003100300032FF1A8D2662374F59989D000D0020003100300033FF1A4E0A67088D265355000D0020003100300034FF1A538653F28D265355000D0020003100300035FF1A79EF5206

[2025-05-21 08:20:58:492_R:] OK

/* Use AT+CSDH=0 to hide header values only in text mode */
[2025-05-21 08:20:59:920_S:] AT+CSDH=0
[2025-05-21 08:20:59:927_R:] AT+CSDH=0

[2025-05-21 08:20:59:936_R:] OK

/* Use ATE1 to enable echo mode */
[2025-05-21 08:27:00:254_S:] ATE1
[2025-05-21 08:27:00:258_R:] ATE1

[2025-05-21 08:27:00:262_R:] OK

/* Use PDU mode */
[2025-05-21 08:27:00:273_S:] AT+CMGF=0
[2025-05-21 08:27:00:277_R:] AT+CMGF=0

[2025-05-21 08:27:00:281_R:] OK

/* Use AT+CMGR=<index> to read a message */
[2025-05-21 08:27:00:291_S:] AT+CMGR=0
[2025-05-21 08:27:00:295_R:] AT+CMGR=0

[2025-05-21 08:27:00:341_R:] +CMGR: 1,,54
[2025-05-21 08:27:00:341_R:] 069168515614066405A00110F00008525012800224232605000322040400680064006400780020FF0C75454EAB4FBF522930104E2D56FD8054901A3011

[2025-05-21 08:27:00:341_R:] OK

/* Use ATE1 to enable echo mode */
[2025-05-21 08:27:05:669_S:] ATE1
[2025-05-21 08:27:05:673_R:] ATE1

[2025-05-21 08:27:05:675_R:] OK

/* Use PDU mode */
[2025-05-21 08:27:05:687_S:] AT+CMGF=0
[2025-05-21 08:27:05:690_R:] AT+CMGF=0

[2025-05-21 08:27:05:697_R:] OK

/* Use AT+CMGR=<index> to read a message */
[2025-05-21 08:27:05:704_S:] AT+CMGR=1
[2025-05-21 08:27:05:710_R:] AT+CMGR=1

[2025-05-21 08:27:05:749_R:] +CMGR: 1,,154
[2025-05-21 08:27:05:749_R:] 069168515614066405A00110F00008525012800224238A05000322040267E58BE2000D0020003100300036FF1A79EF52066D888D398BB05F55000D0020003100300037FF1A624B673A4E0A7F516D4191CF000D0020003100350030FF1A5E38752867E58BE24E1A52A1000D0020598297008FD456DE4E3B83DC5355FF0C8BF753D19001201C00310030003000310030201D81F3003100300030003100303002000D

[2025-05-21 08:27:05:749_R:] OK

/* Use ATE1 to enable echo mode */
[2025-05-21 08:27:33:884_S:] ATE1
[2025-05-21 08:27:33:887_R:] ATE1

[2025-05-21 08:27:33:890_R:] OK

/* Use PDU mode */
[2025-05-21 08:27:33:901_S:] AT+CMGF=0
[2025-05-21 08:27:33:906_R:] AT+CMGF=0

[2025-05-21 08:27:33:911_R:] OK

/* Use AT+CMGR=<index> to read a message */
[2025-05-21 08:27:33:919_S:] AT+CMGR=2
[2025-05-21 08:27:33:925_R:] AT+CMGR=2

[2025-05-21 08:27:33:967_R:] +CMGR: 1,,154
[2025-05-21 08:27:33:967_R:] 069168515614066405A00110F00008525012800224238A05000322040300208BA9670D52A166F467096E295EA6FF014F7F75284E2D56FD8054901A004100500050FF0C8DB34E0D51FA6237529E4E1A52A130014EA48BDD8D39FF0C63075C164E0070B967E54F59989D300167E56D4191CFFF0C70B951FB002000680074007400700073003A002F002F0075002E00310030003000310030002E0063006E002F006B

[2025-05-21 08:27:33:967_R:] OK

/* Use ATE1 to enable echo mode */
[2025-05-21 08:27:48:783_S:] ATE1
[2025-05-21 08:27:48:792_R:] ATE1

[2025-05-21 08:27:48:797_R:] OK

/* Use PDU mode */
[2025-05-21 08:27:48:807_S:] AT+CMGF=0
[2025-05-21 08:27:48:811_R:] AT+CMGF=0

[2025-05-21 08:27:48:811_R:] OK

/* Use AT+CMGR=<index> to read a message */
[2025-05-21 08:27:48:819_S:] AT+CMGR=3
[2025-05-21 08:27:48:824_R:] AT+CMGR=3

[2025-05-21 08:27:48:865_R:] +CMGR: 1,,154
[2025-05-21 08:27:48:865_R:] 069168515614066405A00110F00008525012800224238A0500032204015C0A656C768475286237FF0C8BF756DE590D4EE54E0B7F167801529E74064E1A52A1FF1A000D0020003100300031FF1A5B9E65F68BDD8D39000D0020003100300032FF1A8D2662374F59989D000D0020003100300033FF1A4E0A67088D265355000D0020003100300034FF1A538653F28D265355000D0020003100300035FF1A79EF5206

[2025-05-21 08:27:48:865_R:] OK

/* Use ATE1 to enable echo mode */
[2025-05-21 08:27:57:780_S:] ATE1
[2025-05-21 08:27:57:783_R:] ATE1

[2025-05-21 08:27:57:786_R:] OK

/* Use text mode */
[2025-05-21 08:27:57:799_S:] AT+CMGF=1
[2025-05-21 08:27:57:802_R:] AT+CMGF=1

[2025-05-21 08:27:57:802_R:] OK

/* Use AT+CSDH=1 to show header values only in text mode */
[2025-05-21 08:27:57:811_S:] AT+CSDH=1
[2025-05-21 08:27:57:817_R:] AT+CSDH=1

[2025-05-21 08:27:57:817_R:] OK

/* Set character type */
[2025-05-21 08:27:57:823_S:] AT+CSCS="GSM"
[2025-05-21 08:27:57:827_R:] AT+CSCS="GSM"

[2025-05-21 08:27:57:827_R:] OK

/* Read the message which will change the status of the message */
[2025-05-21 08:27:57:832_S:] AT+CMGR=3
[2025-05-21 08:27:57:837_R:] AT+CMGR=3

[2025-05-21 08:27:57:879_R:] +CMGR: "REC READ","10010",,"25/05/21,08:20:42+32",160,100,0,8,"+8615654160",145,66
[2025-05-21 08:27:57:879_R:] 5C0A656C768475286237FF0C8BF756DE590D4EE54E0B7F167801529E74064E1A52A1FF1A000D0020003100300031FF1A5B9E65F68BDD8D39000D0020003100300032FF1A8D2662374F59989D000D0020003100300033FF1A4E0A67088D265355000D0020003100300034FF1A538653F28D265355000D0020003100300035FF1A79EF5206

[2025-05-21 08:27:57:879_R:] OK

/* Use AT+CSDH=0 to hide header values only in text mode */
[2025-05-21 08:27:59:499_S:] AT+CSDH=0
[2025-05-21 08:27:59:509_R:] AT+CSDH=0

[2025-05-21 08:27:59:509_R:] OK

/* Use ATE1 to enable echo mode */
[2025-05-21 08:28:02:647_S:] ATE1
[2025-05-21 08:28:02:650_R:] ATE1

[2025-05-21 08:28:02:654_R:] OK

/* Use PDU mode */
[2025-05-21 08:28:02:662_S:] AT+CMGF=0
[2025-05-21 08:28:02:669_R:] AT+CMGF=0

[2025-05-21 08:28:02:672_R:] OK

/* Use AT+CMGR=<index> to read a message */
[2025-05-21 08:28:02:682_S:] AT+CMGR=3
[2025-05-21 08:28:02:684_R:] AT+CMGR=3

[2025-05-21 08:28:02:726_R:] +CMGR: 1,,154
[2025-05-21 08:28:02:726_R:] 069168515614066405A00110F00008525012800224238A0500032204015C0A656C768475286237FF0C8BF756DE590D4EE54E0B7F167801529E74064E1A52A1FF1A000D0020003100300031FF1A5B9E65F68BDD8D39000D0020003100300032FF1A8D2662374F59989D000D0020003100300033FF1A4E0A67088D265355000D0020003100300034FF1A538653F28D265355000D0020003100300035FF1A79EF5206

[2025-05-21 08:28:02:726_R:] OK

/* Use ATE1 to enable echo mode */
[2025-05-21 08:28:11:113_S:] ATE1
[2025-05-21 08:28:11:116_R:] ATE1

[2025-05-21 08:28:11:118_R:] OK

/* Use PDU mode */
[2025-05-21 08:28:11:125_S:] AT+CMGF=0
[2025-05-21 08:28:11:132_R:] AT+CMGF=0

[2025-05-21 08:28:11:132_R:] OK

/* Use AT+CMGR=<index> to read a message */
[2025-05-21 08:28:11:135_S:] AT+CMGR=2
[2025-05-21 08:28:11:142_R:] AT+CMGR=2

[2025-05-21 08:28:11:180_R:] +CMGR: 1,,154
[2025-05-21 08:28:11:180_R:] 069168515614066405A00110F00008525012800224238A05000322040300208BA9670D52A166F467096E295EA6FF014F7F75284E2D56FD8054901A004100500050FF0C8DB34E0D51FA6237529E4E1A52A130014EA48BDD8D39FF0C63075C164E0070B967E54F59989D300167E56D4191CFFF0C70B951FB002000680074007400700073003A002F002F0075002E00310030003000310030002E0063006E002F006B

[2025-05-21 08:28:11:180_R:] OK






/* Read all messages */
[2025-05-21 09:10:06:152_S:] AT+CMGL=4
[2025-05-21 09:10:06:159_R:] AT+CMGL=4

[2025-05-21 09:10:06:319_R:] +CMGL: 0,1,,154
[2025-05-21 09:10:06:319_R:] 069168515614166405A00110F00008525012904042238A050003B3040267E58BE2000D0020003100300036FF1A79EF52066D888D398BB05F55000D0020003100300037FF1A624B673A4E0A7F516D4191CF000D0020003100350030FF1A5E38752867E58BE24E1A52A1000D0020598297008FD456DE4E3B83DC5355FF0C8BF753D19001201C00310030003000310030201D81F3003100300030003100303002000D
[2025-05-21 09:10:06:319_R:] +CMGL: 1,1,,154
[2025-05-21 09:10:06:319_R:] 069168515614066405A00110F00008525012904052238A050003B304015C0A656C768475286237FF0C8BF756DE590D4EE54E0B7F167801529E74064E1A52A1FF1A000D0020003100300031FF1A5B9E65F68BDD8D39000D0020003100300032FF1A8D2662374F59989D000D0020003100300033FF1A4E0A67088D265355000D0020003100300034FF1A538653F28D265355000D0020003100300035FF1A79EF5206
[2025-05-21 09:10:06:319_R:] +CMGL: 2,1,,154
[2025-05-21 09:10:06:319_R:] 069168515614066405A00110F00008525012904052238A050003B3040300208BA9670D52A166F467096E295EA6FF014F7F75284E2D56FD8054901A004100500050FF0C8DB34E0D51FA6237529E4E1A52A130014EA48BDD8D39FF0C63075C164E0070B967E54F59989D300167E56D4191CFFF0C70B951FB002000680074007400700073003A002F002F0075002E00310030003000310030002E0063006E002F006B
[2025-05-21 09:10:06:319_R:] +CMGL: 3,1,,54
[2025-05-21 09:10:06:319_R:] 069168515614066405A00110F000085250129040522326050003B3040400680064006400780020FF0C75454EAB4FBF522930104E2D56FD8054901A3011





/* AT+CMGS="10010", and then wait for the">" appears, input your message after the ">", use <CTRL+Z> or 1A (HEX String) to send a message,when receive +CMGS:<index> and OK , means the message has been sent successfully. */
[2025-05-21 19:22:13:568_S:] AT+CMGS="10010"
[2025-05-21 19:22:13:572_R:] AT+CMGS="10010"

[2025-05-21 19:22:13:576_R:] > 1
[2025-05-21 19:22:13:894_R:] +CMGS: 64

[2025-05-21 19:22:13:894_R:] OK

[2025-05-21 19:22:18:895_R:] +CMTI: "SM",1



尊敬的用户，请回复以下编码办理业务：
 201：信用额度
 202：缴费记录查询
 203：业务受理记录查询
 204：用户信息
 205：帐户信息
 206：SP代收费
 207：查询PUK码
 208：套餐查询
 如需返回主菜单，请发送“10010”至10010。
 让服务更有温度！使用中国联通APP，足不出户办业务、交话费，指尖一点查余额、查流量，点击 https://u.10010.cn/khddx ，畅享便利【中国联通】
 
 
 
 
06916851561406240BA13169899137F40000525022203245233FEDF27C1E3E97A1E17379DE0CC3373C38FAED2E3BDFA01568F876DBCB72BA8BFA9ED2E569F719250ED3C768E73BB5F1817A2078F85C9EEF00




