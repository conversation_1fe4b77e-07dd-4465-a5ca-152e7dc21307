use crate::core::types::{GpdSmsError, Result, SmsMessage, SmsType};
use log::{debug, info};
use rusqlite::{params, Connection, Row};
use std::path::PathBuf;
use std::sync::{Arc, Mutex};

pub struct Database {
    connection: Arc<Mutex<Connection>>,
}

impl Database {
    /// Create new database instance
    pub async fn new() -> Result<Self> {
        let db_path = Self::get_database_path()?;

        // Ensure the parent directory exists
        if let Some(parent) = db_path.parent() {
            std::fs::create_dir_all(parent)?;
        }

        let connection = Connection::open(&db_path)?;

        let db = Self {
            connection: Arc::new(Mutex::new(connection))
        };
        db.initialize_tables().await?;

        info!("Database initialized at {}", db_path.display());
        Ok(db)
    }

    /// Get the database file path in the app data directory
    fn get_database_path() -> Result<PathBuf> {
        // Use app data directory instead of current directory
        let app_data_dir = dirs::data_dir()
            .ok_or_else(|| GpdSmsError::Other("Failed to get app data directory".to_string()))?
            .join("GPDSMS-Rust");

        Ok(app_data_dir.join("storage.db"))
    }

    /// Initialize database tables
    async fn initialize_tables(&self) -> Result<()> {
        let create_table_sql = r#"
            CREATE TABLE IF NOT EXISTS history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                phone_no TEXT NOT NULL,
                phone_to TEXT NOT NULL,
                create_date DATETIME NOT NULL,
                message TEXT NOT NULL,
                type TEXT NOT NULL,
                batch_no INTEGER DEFAULT 0,
                order_no INTEGER DEFAULT 0
            )
        "#;

        let conn = self.connection.lock().unwrap();
        conn.execute(create_table_sql, [])?;

        // Create index for better performance
        let create_index_sql = r#"
            CREATE INDEX IF NOT EXISTS idx_history_date
            ON history(create_date DESC)
        "#;

        conn.execute(create_index_sql, [])?;

        debug!("Database tables initialized");
        Ok(())
    }

    /// Save SMS message to database
    pub async fn save_sms(&self, sms: &SmsMessage) -> Result<i64> {
        let insert_sql = r#"
            INSERT INTO history (phone_no, phone_to, create_date, message, type, batch_no, order_no)
            VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7)
        "#;

        let message_type = match sms.message_type {
            SmsType::Received => "received",
            SmsType::Sent => "sent",
        };

        let conn = self.connection.lock().unwrap();
        conn.execute(
            insert_sql,
            params![
                sms.phone_no,
                sms.phone_to,
                sms.create_date,
                sms.message,
                message_type,
                sms.batch_no,
                sms.order_no
            ],
        )?;

        let id = conn.last_insert_rowid();
        debug!("Saved SMS with ID: {}", id);
        Ok(id)
    }

    /// Get SMS history with pagination
    pub async fn get_sms_history(&self, limit: u32, offset: u32) -> Result<Vec<SmsMessage>> {
        let query_sql = r#"
            SELECT id, phone_no, phone_to, create_date, message, type, batch_no, order_no
            FROM history
            ORDER BY id DESC, order_no DESC
            LIMIT ?1 OFFSET ?2
        "#;

        let conn = self.connection.lock().unwrap();
        let mut stmt = conn.prepare(query_sql)?;
        let sms_iter = stmt.query_map(params![limit, offset], |row| {
            Ok(Self::row_to_sms_message(row)?)
        })?;

        let mut messages = Vec::new();
        for sms in sms_iter {
            messages.push(sms?);
        }

        debug!("Retrieved {} SMS messages", messages.len());
        Ok(messages)
    }

    /// Get SMS history for specific phone number
    pub async fn get_sms_history_for_phone(&self, phone_no: &str, limit: u32) -> Result<Vec<SmsMessage>> {
        let query_sql = r#"
            SELECT id, phone_no, phone_to, create_date, message, type, batch_no, order_no
            FROM history
            WHERE phone_no = ?1 OR phone_to = ?1
            ORDER BY id DESC
            LIMIT ?2
        "#;

        let conn = self.connection.lock().unwrap();
        let mut stmt = conn.prepare(query_sql)?;
        let sms_iter = stmt.query_map(params![phone_no, limit], |row| {
            Ok(Self::row_to_sms_message(row)?)
        })?;

        let mut messages = Vec::new();
        for sms in sms_iter {
            messages.push(sms?);
        }

        debug!("Retrieved {} SMS messages for phone {}", messages.len(), phone_no);
        Ok(messages)
    }

    /// Delete SMS message by ID
    pub async fn delete_sms(&self, id: i64) -> Result<bool> {
        let delete_sql = "DELETE FROM history WHERE id = ?1";
        let conn = self.connection.lock().unwrap();
        let rows_affected = conn.execute(delete_sql, params![id])?;

        let deleted = rows_affected > 0;
        if deleted {
            debug!("Deleted SMS with ID: {}", id);
        } else {
            debug!("No SMS found with ID: {}", id);
        }

        Ok(deleted)
    }

    /// Delete all SMS messages
    pub async fn delete_all_sms(&self) -> Result<u32> {
        let delete_sql = "DELETE FROM history";
        let conn = self.connection.lock().unwrap();
        let rows_affected = conn.execute(delete_sql, [])?;

        info!("Deleted {} SMS messages", rows_affected);
        Ok(rows_affected as u32)
    }

    /// Search SMS messages by content
    pub async fn search_sms(&self, search_term: &str, limit: u32) -> Result<Vec<SmsMessage>> {
        let query_sql = r#"
            SELECT id, phone_no, phone_to, create_date, message, type, batch_no, order_no
            FROM history
            WHERE message LIKE ?1 OR phone_no LIKE ?1 OR phone_to LIKE ?1
            ORDER BY id DESC
            LIMIT ?2
        "#;

        let search_pattern = format!("%{}%", search_term);
        let conn = self.connection.lock().unwrap();
        let mut stmt = conn.prepare(query_sql)?;
        let sms_iter = stmt.query_map(params![search_pattern, limit], |row| {
            Ok(Self::row_to_sms_message(row)?)
        })?;

        let mut messages = Vec::new();
        for sms in sms_iter {
            messages.push(sms?);
        }

        debug!("Found {} SMS messages matching '{}'", messages.len(), search_term);
        Ok(messages)
    }

    /// Get SMS statistics
    pub async fn get_sms_statistics(&self) -> Result<SmsStatistics> {
        let stats_sql = r#"
            SELECT
                COUNT(*) as total,
                COUNT(CASE WHEN type = 'received' THEN 1 END) as received,
                COUNT(CASE WHEN type = 'sent' THEN 1 END) as sent,
                COUNT(DISTINCT phone_no) as unique_contacts
            FROM history
        "#;

        let conn = self.connection.lock().unwrap();
        let mut stmt = conn.prepare(stats_sql)?;
        let stats = stmt.query_row([], |row| {
            Ok(SmsStatistics {
                total_messages: row.get(0)?,
                received_messages: row.get(1)?,
                sent_messages: row.get(2)?,
                unique_contacts: row.get(3)?,
            })
        })?;

        debug!("SMS statistics: {:?}", stats);
        Ok(stats)
    }

    /// Convert database row to SmsMessage
    fn row_to_sms_message(row: &Row) -> rusqlite::Result<SmsMessage> {
        let message_type_str: String = row.get(5)?;
        let message_type = match message_type_str.as_str() {
            "received" => SmsType::Received,
            "sent" => SmsType::Sent,
            _ => SmsType::Received, // Default fallback
        };

        // Handle different date formats - try string first, then integer timestamp
        let create_date = match row.get::<_, String>(3) {
            Ok(date_str) => date_str,
            Err(_) => {
                // If string fails, try to get as integer timestamp and convert
                match row.get::<_, i64>(3) {
                    Ok(timestamp) => {
                        // Convert Unix timestamp to readable format
                        let datetime = chrono::DateTime::from_timestamp(timestamp, 0)
                            .unwrap_or_else(|| chrono::Utc::now());
                        datetime.format("%Y-%m-%d %H:%M:%S").to_string()
                    }
                    Err(_) => {
                        // Fallback to current time if both fail
                        chrono::Utc::now().format("%Y-%m-%d %H:%M:%S").to_string()
                    }
                }
            }
        };

        Ok(SmsMessage {
            id: Some(row.get(0)?),
            phone_no: row.get(1)?,
            phone_to: row.get(2)?,
            create_date,
            message: row.get(4)?,
            message_type,
            batch_no: row.get(6)?,
            order_no: row.get(7)?,
        })
    }

    /// Vacuum database to reclaim space
    pub async fn vacuum(&self) -> Result<()> {
        let conn = self.connection.lock().unwrap();
        conn.execute("VACUUM", [])?;
        info!("Database vacuumed");
        Ok(())
    }

    /// Get database file size
    pub async fn get_database_size(&self) -> Result<u64> {
        let db_path = Self::get_database_path()?;
        if db_path.exists() {
            let metadata = std::fs::metadata(&db_path)?;
            Ok(metadata.len())
        } else {
            Ok(0)
        }
    }
}

#[derive(Debug)]
pub struct SmsStatistics {
    pub total_messages: u32,
    pub received_messages: u32,
    pub sent_messages: u32,
    pub unique_contacts: u32,
}
