use crate::core::{
    database::Database,
    serial_manager::SerialManager,
    sms_manager::SmsManager,
};
use std::sync::Arc;
use tokio::sync::Mutex;

/// Application state shared between GUI and backend
#[derive(Clone)]
pub struct AppState {
    pub database: Arc<Mutex<Database>>,
    pub serial_manager: Arc<Mutex<SerialManager>>,
    pub sms_manager: Arc<Mutex<SmsManager>>,
}

impl AppState {
    pub async fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let database = Arc::new(Mutex::new(Database::new().await?));
        let serial_manager = Arc::new(Mutex::new(SerialManager::new()));
        let sms_manager = Arc::new(Mutex::new(SmsManager::new()));
        
        Ok(Self {
            database,
            serial_manager,
            sms_manager,
        })
    }
    
    pub fn from_components(
        database: Arc<Mutex<Database>>,
        serial_manager: Arc<Mutex<SerialManager>>,
        sms_manager: Arc<Mutex<SmsManager>>,
    ) -> Self {
        Self {
            database,
            serial_manager,
            sms_manager,
        }
    }
}
