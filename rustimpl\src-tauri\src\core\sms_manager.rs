use crate::core::types::{GpdSmsError, Result, SmsMessage, SmsType};
use crate::core::pdu_tool::PduTool;
use crate::core::serial_manager::SerialManager;
use log::{debug, info};
use std::collections::HashMap;

pub struct SmsManager {
    multipart_messages: HashMap<String, Vec<SmsMessage>>,
}

impl SmsManager {
    pub fn new() -> Self {
        Self {
            multipart_messages: HashMap::new(),
        }
    }

    /// Send SMS message
    pub async fn send_sms(
        &self,
        serial_manager: &SerialManager,
        phone_number: &str,
        message: &str,
    ) -> Result<Vec<String>> {
        if !serial_manager.is_connected() {
            return Err(GpdSmsError::Other("Serial port not connected".to_string()));
        }

        // Check if message needs to be split
        let message_parts = self.split_message(message);
        let mut results = Vec::new();

        for (index, part) in message_parts.iter().enumerate() {
            let pdu = if message_parts.len() > 1 {
                self.encode_multipart_sms(phone_number, part, index + 1, message_parts.len())?
            } else {
                self.encode_single_sms(phone_number, part)?
            };

            let response = serial_manager.send_sms_pdu(&pdu).await?;
            results.push(response);

            debug!("Sent SMS part {}/{} to {}", index + 1, message_parts.len(), phone_number);
        }

        info!("Successfully sent {} SMS parts to {}", message_parts.len(), phone_number);
        Ok(results)
    }

    /// Process received SMS PDU
    pub async fn process_received_sms(&mut self, pdu: &str) -> Result<Option<SmsMessage>> {
        let pdu_message = PduTool::decode_full_pdu(pdu)?;

        // Check if this is a multipart message
        if self.is_multipart_message(pdu) {
            return self.handle_multipart_message(pdu_message, pdu).await;
        }

        // Single message
        let sms = SmsMessage {
            id: None,
            phone_no: pdu_message.phone_no,
            phone_to: String::new(), // Will be filled by caller
            message: pdu_message.content,
            create_date: pdu_message.timestamp,
            message_type: SmsType::Received,
            batch_no: 0,
            order_no: 0,
        };

        debug!("Processed single SMS from {}", sms.phone_no);
        Ok(Some(sms))
    }

    /// Split long message into parts
    fn split_message(&self, message: &str) -> Vec<String> {
        const MAX_SINGLE_SMS_LENGTH: usize = 160;
        const MAX_MULTIPART_SMS_LENGTH: usize = 153; // 160 - 7 bytes for UDH

        if message.len() <= MAX_SINGLE_SMS_LENGTH {
            return vec![message.to_string()];
        }

        let mut parts = Vec::new();
        let chars: Vec<char> = message.chars().collect();
        let mut start = 0;

        while start < chars.len() {
            let end = (start + MAX_MULTIPART_SMS_LENGTH).min(chars.len());
            let part: String = chars[start..end].iter().collect();
            parts.push(part);
            start = end;
        }

        debug!("Split message into {} parts", parts.len());
        parts
    }

    /// Encode single SMS to PDU format
    fn encode_single_sms(&self, phone_number: &str, message: &str) -> Result<String> {
        let mut pdu = String::new();

        // SMSC (empty)
        pdu.push_str("00");

        // PDU Type (SMS-SUBMIT, no status report)
        pdu.push_str("01");

        // Message Reference (will be set by modem)
        pdu.push_str("00");

        // Destination Address
        let encoded_phone = self.encode_phone_number(phone_number)?;
        pdu.push_str(&format!("{:02X}", phone_number.len()));
        pdu.push_str("91"); // International format
        pdu.push_str(&encoded_phone);

        // Protocol Identifier
        pdu.push_str("00");

        // Data Coding Scheme (Unicode)
        pdu.push_str("08");

        // Validity Period (not used)
        // User Data
        let encoded_message = self.encode_unicode_message(message)?;
        pdu.push_str(&format!("{:02X}", encoded_message.len() / 2));
        pdu.push_str(&encoded_message);

        debug!("Encoded single SMS PDU: {} chars", pdu.len());
        Ok(pdu)
    }

    /// Encode multipart SMS to PDU format
    fn encode_multipart_sms(
        &self,
        phone_number: &str,
        message: &str,
        part_number: usize,
        total_parts: usize,
    ) -> Result<String> {
        let mut pdu = String::new();

        // SMSC (empty)
        pdu.push_str("00");

        // PDU Type (SMS-SUBMIT with UDH)
        pdu.push_str("41");

        // Message Reference
        pdu.push_str("00");

        // Destination Address
        let encoded_phone = self.encode_phone_number(phone_number)?;
        pdu.push_str(&format!("{:02X}", phone_number.len()));
        pdu.push_str("91");
        pdu.push_str(&encoded_phone);

        // Protocol Identifier
        pdu.push_str("00");

        // Data Coding Scheme (Unicode)
        pdu.push_str("08");

        // User Data with UDH
        let encoded_message = self.encode_unicode_message(message)?;
        let udh = format!("050003{:02X}{:02X}{:02X}",
                         self.generate_reference_number(),
                         total_parts,
                         part_number);

        let total_length = (udh.len() + encoded_message.len()) / 2;
        pdu.push_str(&format!("{:02X}", total_length));
        pdu.push_str(&udh);
        pdu.push_str(&encoded_message);

        debug!("Encoded multipart SMS PDU part {}/{}", part_number, total_parts);
        Ok(pdu)
    }

    /// Encode phone number to PDU format
    fn encode_phone_number(&self, phone_number: &str) -> Result<String> {
        let mut encoded = String::new();
        let digits: Vec<char> = phone_number.chars().filter(|c| c.is_ascii_digit()).collect();

        for i in (0..digits.len()).step_by(2) {
            if i + 1 < digits.len() {
                encoded.push(digits[i + 1]);
                encoded.push(digits[i]);
            } else {
                encoded.push('F');
                encoded.push(digits[i]);
            }
        }

        Ok(encoded)
    }

    /// Encode message to Unicode hex
    fn encode_unicode_message(&self, message: &str) -> Result<String> {
        let mut encoded = String::new();

        for ch in message.chars() {
            encoded.push_str(&format!("{:04X}", ch as u32));
        }

        Ok(encoded)
    }

    /// Check if PDU represents a multipart message
    fn is_multipart_message(&self, pdu: &str) -> bool {
        // Simple check - look for UDH indicator in PDU type
        if pdu.len() > 4 {
            if let Ok(pdu_type) = u8::from_str_radix(&pdu[2..4], 16) {
                return (pdu_type & 0x40) != 0; // UDH indicator bit
            }
        }
        false
    }

    /// Handle multipart message assembly
    async fn handle_multipart_message(
        &mut self,
        pdu_message: crate::core::types::PduMessage,
        _pdu: &str,
    ) -> Result<Option<SmsMessage>> {
        // For now, return the part as a single message
        // TODO: Implement proper multipart message assembly
        let sms = SmsMessage {
            id: None,
            phone_no: pdu_message.phone_no,
            phone_to: String::new(),
            message: format!("[Part] {}", pdu_message.content),
            create_date: pdu_message.timestamp,
            message_type: SmsType::Received,
            batch_no: 0,
            order_no: 0,
        };

        debug!("Processed multipart SMS part from {}", sms.phone_no);
        Ok(Some(sms))
    }

    /// Generate reference number for multipart messages
    fn generate_reference_number(&self) -> u8 {
        use std::time::{SystemTime, UNIX_EPOCH};
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        (timestamp % 256) as u8
    }

    /// Get message statistics
    pub fn get_statistics(&self) -> SmsStatistics {
        SmsStatistics {
            pending_multipart: self.multipart_messages.len(),
        }
    }
}

#[derive(Debug)]
pub struct SmsStatistics {
    pub pending_multipart: usize,
}
