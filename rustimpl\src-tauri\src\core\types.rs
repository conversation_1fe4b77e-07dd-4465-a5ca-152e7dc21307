use serde::{Deserialize, Serialize};
use std::fmt;

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct SmsMessage {
    pub id: Option<i64>,
    pub phone_no: String,
    pub phone_to: String,
    pub message: String,
    pub create_date: String,
    pub message_type: SmsType,
    pub batch_no: i32,
    pub order_no: i32,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum SmsType {
    Received,
    Sent,
}

impl fmt::Display for SmsType {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match self {
            SmsType::Received => write!(f, "received"),
            SmsType::Sent => write!(f, "sent"),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SerialPortInfo {
    pub port_name: String,
    pub description: Option<String>,
    pub hardware_id: Option<String>,
}

#[derive(Debug, <PERSON>lone)]
pub struct PduMessage {
    pub phone_no: String,
    pub timestamp: String,
    pub content: String,
}

#[derive(Debug, thiserror::Error)]
pub enum GpdSmsError {
    #[error("Serial port error: {0}")]
    SerialPort(#[from] serialport::Error),
    
    #[error("Database error: {0}")]
    Database(#[from] rusqlite::Error),
    
    #[error("PDU decode error: {0}")]
    PduDecode(String),
    
    #[error("SMS send error: {0}")]
    SmsSend(String),
    
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("Other error: {0}")]
    Other(String),
}

pub type Result<T> = std::result::Result<T, GpdSmsError>;
