[package]
name = "gpdsms-rust"
version = "1.5.0"
description = "SMS management application for Quectel 4G modules on PC"
authors = ["GPDSMS Team"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "gpdsms_rust_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
# GUI Framework - Using Tauri 2 for cross-platform desktop app
tauri = { version = "2", features = ["tray-icon"] }
tauri-plugin-opener = "2"
tauri-plugin-shell = "2"
tauri-plugin-dialog = "2"
tauri-plugin-fs = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"

# Serial port communication
serialport = "4.3"
tokio = { version = "1.0", features = ["full"] }

# Database
rusqlite = { version = "0.30", features = ["bundled", "chrono"] }

# Async runtime and utilities
futures = "0.3"
chrono = { version = "0.4", features = ["serde"] }

# Logging
log = "0.4"
env_logger = "0.10"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Encoding and text processing
encoding_rs = "0.8"
regex = "1.10"

# System integration
winreg = "0.52"  # Windows registry access

# Directory utilities
dirs = "5.0"

# Windows specific dependencies
[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = ["fileapi", "winbase", "minwindef", "winnt"] }

