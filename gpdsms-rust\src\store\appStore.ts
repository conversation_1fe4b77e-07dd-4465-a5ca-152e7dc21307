import { create } from 'zustand';

export interface SmsMessage {
  id?: number;
  phone_no: string;
  phone_to: string;
  message: string;
  create_date: string;
  message_type: 'received' | 'sent';
  batch_no: number;
  order_no: number;
}

interface AppState {
  // Connection state
  connectionStatus: boolean;
  setConnectionStatus: (status: boolean) => void;
  
  // Messages
  messages: SmsMessage[];
  setMessages: (messages: SmsMessage[]) => void;
  addMessage: (message: SmsMessage) => void;
  removeMessage: (id: number) => void;
  
  // UI state
  unreadCount: number;
  setUnreadCount: (count: number) => void;
  
  // Settings
  settings: {
    autoStart: boolean;
    minimizeToTray: boolean;
    showNotifications: boolean;
    notificationSound: boolean;
    autoReconnect: boolean;
    reconnectInterval: number;
    connectionTimeout: number;
    autoCleanup: boolean;
    retentionDays: number;
  };
  updateSettings: (newSettings: Partial<AppState['settings']>) => void;
  
  // Loading states
  loading: {
    connecting: boolean;
    sendingSms: boolean;
    loadingMessages: boolean;
  };
  setLoading: (key: keyof AppState['loading'], value: boolean) => void;
}

export const useAppStore = create<AppState>((set, get) => ({
  // Connection state
  connectionStatus: false,
  setConnectionStatus: (status) => set({ connectionStatus: status }),
  
  // Messages
  messages: [],
  setMessages: (messages) => set({ messages }),
  addMessage: (message) => set((state) => ({ 
    messages: [message, ...state.messages] 
  })),
  removeMessage: (id) => set((state) => ({ 
    messages: state.messages.filter(msg => msg.id !== id) 
  })),
  
  // UI state
  unreadCount: 0,
  setUnreadCount: (count) => set({ unreadCount: count }),
  
  // Settings with defaults
  settings: {
    autoStart: false,
    minimizeToTray: true,
    showNotifications: true,
    notificationSound: true,
    autoReconnect: true,
    reconnectInterval: 30,
    connectionTimeout: 10,
    autoCleanup: false,
    retentionDays: 90,
  },
  updateSettings: (newSettings) => set((state) => ({
    settings: { ...state.settings, ...newSettings }
  })),
  
  // Loading states
  loading: {
    connecting: false,
    sendingSms: false,
    loadingMessages: false,
  },
  setLoading: (key, value) => set((state) => ({
    loading: { ...state.loading, [key]: value }
  })),
}));

// Persist settings to localStorage
export const loadSettings = () => {
  try {
    const savedSettings = localStorage.getItem('gpdsms-settings');
    if (savedSettings) {
      const settings = JSON.parse(savedSettings);
      useAppStore.getState().updateSettings(settings);
    }
  } catch (error) {
    console.error('Failed to load settings:', error);
  }
};

export const saveSettings = (settings: Partial<AppState['settings']>) => {
  try {
    const currentSettings = useAppStore.getState().settings;
    const newSettings = { ...currentSettings, ...settings };
    localStorage.setItem('gpdsms-settings', JSON.stringify(newSettings));
    useAppStore.getState().updateSettings(settings);
  } catch (error) {
    console.error('Failed to save settings:', error);
  }
};
