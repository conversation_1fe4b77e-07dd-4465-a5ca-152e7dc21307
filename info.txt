AT+CMGF=1

AT+CSCS="GSM"

AT+CMGS="13969819734"

test


AT+CMGF=0

AT+CSCS="USC2"


AT+CMGS=<length><CR>
PDU is given <Ctrl+Z/ESC>

AT+CMGS="13969819734"


+CSCA: "002B0038003600310033003000310030003100360036003500300030",145\r\n\r\nOK



+CMTI: "ME",134+CMGR: 0,,28
0891683190106605F0240D91683169899137F400003210310070732309B1984C36B3D56837

OK


+CMGR: 0,,28\r\n0891683190106605F0240D91683169899137F40008321031003381230862115F88597DFF1F\r\n\r\nOK



+CMGR: 0,,28\r\n0891683190106605F0240D91683169899137F40008321031004364230862115F88597D5440\r\n\r\nOK



"+CMGS: 16\r\n\r\nOK"

AT+CMGR=166

0891683190106605F06405A00110F0000832103151232123880500030804015C0A656C768475286237FF0C8BF756DE590D4EE54E0B7F167801529E74064E1A52A1FF1A000D000A0020003100300031FF1A5B9E65F68BDD8D39000D000A0020003100300032FF1A8D2662374F59989D000D000A0020003100300033FF1A4E0A67088D265355000D000A0020003100300034FF1A538653F28D265355000D000A0020069168515614066405A00110F000083210315123412388050003080402003100300035FF1A79EF520667E58BE2000D000A0020003100300036FF1A79EF52066D888D398BB05F55000D000A0020003100300037FF1A624B673A4E0A7F516D4191CF000D000A0020003100300038FF1A79EF52064EA7751F8BB05F55000D000A0020003100350030FF1A5E38752867E58BE24E1A52A1000D000A002059829700069168515614066405A00110F0000832103151238123880500030804038FD456DE4E3B83DC5355FF0C8BF753D19001201C00310030003000310030201D81F3003100300030003100303002000D000A002030108BA9670D52A166F467096E295EA6FF014F7F75284E2D56FD8054901A004100500050FF0C8DB34E0D51FA62374EA48BDD8D39300167E54F59989D3001529E4E1A52A1FF0C514D6D4191CF770B069168515614166405A00110F00008321031512322235605000308040475355F71300173A96E38620FFF0C70B951FB00200068007400740070003A002F002F0075002E00310030003000310030002E0063006E002F006B00680064006400780020FF0C9A6C4E0A62E567093011


严重性	代码	说明	项目	文件	行	禁止显示状态
错误	CS0656	Missing compiler required member 'Microsoft.CSharp.RuntimeBinder.Binder.Convert'	GPDSMS	E:\Projects\C#\GPDSMS\GPDSMS\Form1.cs	244	活动



计算机\HKEY_LOCAL_MACHINE\SYSTEM\ControlSet001\Control\Class\{4d36e978-e325-11ce-bfc1-08002be10318}\0000



5E387528529E74064E1A52A1000D000A002000300034FF1A5E38752867E58BE24E1A52A1000D000A002000300035FF1A5145503C53615145503C000D000A002030108BA9670D52A166F467096E295EA6FF014F7F75284E2D56FD8054901A004100500050FF0C8DB34E0D51FA62374EA48BDD8D39300167E54F59989D3001529E4E1A

E58BE24E1A52A1000D000A002000300035FF1A5145503C53615145503C000D000A002030108BA9670D52A166F467096E295EA6FF014F7F75284E2D56FD8054901A004100500050FF0C8DB34E0D51FA62374EA48BDD8D39300167E54F59989D3001529E4E1A



069168515614066405A00110F0000832103151238123880500030804038FD456DE4E3B83DC5355FF0C8BF753D19001201C00310030003000310030201D81F3003100300030003100303002000D000A002030108BA9670D52A166F467096E295EA6FF014F7F75284E2D56FD8054901A004100500050FF0C8DB34E0D51FA62374EA48BDD8D39300167E54F59989D3001529E4E1A52A1FF0C514D6D4191CF770B



+CMTI: \"ME\",174\r\n\r\n+CMTI: \"ME\",175



069168515614166405A00110F0000832103171836423880500030A04015C0A656C768475286237FF0C8BF756DE590D4EE54E0B7F167801529E74064E1A52A1FF1A000D000A0020003100300031FF1A5B9E65F68BDD8D39000D000A0020003100300032FF1A8D2662374F59989D000D000A0020003100300033FF1A4E0A67088D265355000D000A0020003100300034FF1A538653F28D265355000D000A0020

0891683190105705F06405A00110F0000832103171839423880500030A0402003100300035FF1A79EF520667E58BE2000D000A0020003100300036FF1A79EF52066D888D398BB05F55000D000A0020003100300037FF1A624B673A4E0A7F516D4191CF000D000A0020003100300038FF1A79EF52064EA7751F8BB05F55000D000A0020003100350030FF1A5E38752867E58BE24E1A52A1000D000A002059829700

069168515614166405A00110F0000832103171834523880500030A04038FD456DE4E3B83DC5355FF0C8BF753D19001201C00310030003000310030201D81F3003100300030003100303002000D000A002030108BA9670D52A166F467096E295EA6FF014F7F75284E2D56FD8054901A004100500050FF0C8DB34E0D51FA62374EA48BDD8D39300167E54F59989D3001529E4E1A52A1FF0C514D6D4191CF770B

069168515614066405A00110F0000832103171836523560500030A040475355F71300173A96E38620FFF0C70B951FB00200068007400740070003A002F002F0075002E00310030003000310030002E0063006E002F006B00680064006400780020FF0C9A6C4E0A62E567093011



003100300035FF1A79EF520667E58BE2000D000A0020003100300036FF1A79EF52066D888D398BB05F55000D000A0020003100300037FF1A624B673A4E0A7F516D4191CF000D000A0020003100300038FF1A79EF52064EA7751F8BB05F55000D000A0020003100350030FF1A5E38752867E58BE24E1A52A1000D000A002059829700


069168515614166405A00110F0000832103171944023880500030B0402003100300035FF1A79EF520667E58BE2000D000A0020003100300036FF1A79EF52066D888D398BB05F55000D000A0020003100300037FF1A624B673A4E0A7F516D4191CF000D000A0020003100300038FF1A79EF52064EA7751F8BB05F55000D000A0020003100350030FF1A5E38752867E58BE24E1A52A1000D000A002059829700

069168515614166405A00110F0000832103171945023560500030B040475355F71300173A96E38620FFF0C70B951FB00200068007400740070003A002F002F0075002E00310030003000310030002E0063006E002F006B00680064006400780020FF0C9A6C4E0A62E567093011

069168515614066405A00110F0000832103171156423880500030C0402003100300035FF1A79EF520667E58BE2000D000A0020003100300036FF1A79EF52066D888D398BB05F55000D000A0020003100300037FF1A624B673A4E0A7F516D4191CF000D000A0020003100300038FF1A79EF52064EA7751F8BB05F55000D000A0020003100350030FF1A5E38752867E58BE24E1A52A1000D000A002059829700

0891683190105705F06405A00110F0000832103171154523560500030C040475355F71300173A96E38620FFF0C70B951FB00200068007400740070003A002F002F0075002E00310030003000310030002E0063006E002F006B00680064006400780020FF0C9A6C4E0A62E567093011

0891683190106605F06405A00110F0000832103171154523880500030C04038FD456DE4E3B83DC5355FF0C8BF753D19001201C00310030003000310030201D81F3003100300030003100303002000D000A002030108BA9670D52A166F467096E295EA6FF014F7F75284E2D56FD8054901A004100500050FF0C8DB34E0D51FA62374EA48BDD8D39300167E54F59989D3001529E4E1A52A1FF0C514D6D4191CF770B
069168515614166405A00110F0000832103171156523880500030C04015C0A656C768475286237FF0C8BF756DE590D4EE54E0B7F167801529E74064E1A52A1FF1A000D000A0020003100300031FF1A5B9E65F68BDD8D39000D000A0020003100300032FF1A8D2662374F59989D000D000A0020003100300033FF1A4E0A67088D265355000D000A0020003100300034FF1A538653F28D265355000D000A0020

069168515614066405A00110F0000832103181300123880500030D04015C0A656C768475286237FF0C8BF756DE590D4EE54E0B7F167801529E74064E1A52A1FF1A000D000A0020003100300031FF1A5B9E65F68BDD8D39000D000A0020003100300032FF1A8D2662374F59989D000D000A0020003100300033FF1A4E0A67088D265355000D000A0020003100300034FF1A538653F28D265355000D000A0020

-------------------------186
+CMGR: "REC UNREAD","10010",,"23/01/13,18:03:10+32"
5C0A656C768475286237FF0C8BF756DE590D4EE54E0B7F167801529E74064E1A52A1FF1A000D000A0020003100300031FF1A5B9E65F68BDD8D39000D000A0020003100300032FF1A8D2662374F59989D000D000A0020003100300033FF1A4E0A67088D265355000D000A0020003100300034FF1A538653F28D265355000D000A0020

OK



+CMGR: \"REC UNREAD\",\"10010\",,\"23/01/14,13:10:40+32\"\r\n5C0A656C768475286237FF0C8BF756DE590D4EE54E0B7F167801529E74064E1A52A1FF1A000D000A0020003100300031FF1A5B9E65F68BDD8D39000D000A0020003100300032FF1A8D2662374F59989D000D000A0020003100300033FF1A4E0A67088D265355000D000A0020003100300034FF1A538653F28D265355000D000A0020\r\n\r\nOK

5C0A656C768475286237FF0C8BF756DE590D4EE54E0B7F167801529E74064E1A52A1FF1A000D000A0020003100300031FF1A5B9E65F68BDD8D39000D000A0020003100300032FF1A8D2662374F59989D000D000A0020003100300033FF1A4E0A67088D265355000D000A0020003100300034FF1A538653F28D265355000D000A0020\r\n\r\nOK


5C0A656C768475286237FF0C8BF756DE590D4EE54E0B7F167801529E74064E1A52A1FF1A000D000A0020003100300031FF1A5B9E65F68BDD8D39000D000A0020003100300032FF1A8D2662374F59989D000D000A0020003100300033FF1A4E0A67088D265355000D000A0020003100300034FF1A538653F28D265355000D000A0020


069168515614166405A00110F0000832104131436123880500031C04015C0A656C768475286237FF0C8BF756DE590D4EE54E0B7F167801529E74064E1A52A1FF1A000D000A0020003100300031FF1A5B9E65F68BDD8D39000D000A0020003100300032FF1A8D2662374F59989D000D000A0020003100300033FF1A4E0A67088D265355000D000A0020003100300034FF1A538653F28D265355000D000A0020



+CMTI: "ME",24+CMTI: "ME",25+CMTI: "ME",26+CMTI: "ME",2720230114133529
10010
Ԁ̝Ё尊敬的用户，请回复以下编码办理业务：
 101：实时话费
 102：账户余额
 103：上月账单
 104：历史账单
 
20230114133530
10010
Ԁ̝Ђ105：积分查询
 106：积分消费记录
 107：手机上网流量
 108：积分产生记录
 150：常用查询业务
 如需
20230114133534
10010
Ԁ̝Ѓ返回主菜单，请发送“10010”至10010。
 【让服务更有温度！使用中国联通APP，足不出户交话费、查余额、办业务，免流量看
20230114133539
10010
Ԁ̝Є电影、玩游戏，点击 http://u.10010.cn/khddx ，马上拥有】



+CMTI: "ME",2820230114134011
8613969819734
你好测试123
+CMTI: "ME",2920230114134052
8613969819734
ԀϖԄ复字母（如d）预约10086电话客服回访为您解决如下问题，有机会享500MB流量券。
【d】我想改个更合适的套餐
【t】装宽带，享30

ԀϖԂ括：
- 套餐及固定费68.00元；
- 套餐外短彩信费0.70元；
- 其他费用-10.00元；
【查询账单详情】可点击 https

Ԁϖԃ://f.10086.cn/s/#Xcozd 
更多信息详询10086。退订请回复TDZD 
----------------
↓↓回

Ԁϖԅ元话费
心级服务，让爱连接【中国移动】

Ԁϖԁ【话费账单】尊敬的139****9734客户，您2022年12月1日-2022年12月31日共消费58.70元。其中:
主要消费项目包