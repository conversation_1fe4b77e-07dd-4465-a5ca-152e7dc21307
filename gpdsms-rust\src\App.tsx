import React, { useState, useEffect } from 'react';
import { Layout, Button, Select, Card, Badge, Dropdown, message, Space, Typography } from 'antd';
import {
  PhoneOutlined,
  PlusOutlined,
  SettingOutlined,
  WifiOutlined,
  SignalFilled,
  MenuOutlined
} from '@ant-design/icons';
import { invoke } from '@tauri-apps/api/core';
import MessageList from './components/MessageList';
import SendSmsDialog from './components/SendSmsDialog';
import SettingsDialog from './components/SettingsDialog';
import { useAppStore } from './store/appStore';

const { Header, Sider, Content } = Layout;
const { Option } = Select;
const { Title } = Typography;

interface SerialPortInfo {
  port_name: string;
  description?: string;
  hardware_id?: string;
}

const App: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [availablePorts, setAvailablePorts] = useState<SerialPortInfo[]>([]);
  const [selectedPort, setSelectedPort] = useState<string>('');
  const [connecting, setConnecting] = useState(false);
  const [showSendDialog, setShowSendDialog] = useState(false);
  const [showSettingsDialog, setShowSettingsDialog] = useState(false);
  const [signalStrength, setSignalStrength] = useState(0);

  const {
    connectionStatus,
    setConnectionStatus,
    unreadCount
  } = useAppStore();

  useEffect(() => {
    refreshPorts();
    checkConnectionStatus();
  }, []);

  useEffect(() => {
    let interval: number;
    if (connectionStatus) {
      interval = setInterval(async () => {
        try {
          const strength = await invoke<number>('get_signal_strength');
          setSignalStrength(strength);
        } catch (error) {
          console.error('获取信号强度失败:', error);
        }
      }, 5000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [connectionStatus]);

  const refreshPorts = async () => {
    try {
      const ports = await invoke<SerialPortInfo[]>('get_serial_ports');
      setAvailablePorts(ports);
    } catch (error) {
      message.error('获取串口列表失败: ' + error);
    }
  };

  const checkConnectionStatus = async () => {
    try {
      const status = await invoke<boolean>('get_connection_status');
      setConnectionStatus(status);
    } catch (error) {
      console.error('获取连接状态失败:', error);
    }
  };

  const toggleConnection = async () => {
    if (connectionStatus) {
      await disconnect();
    } else {
      await connect();
    }
  };

  const connect = async () => {
    if (!selectedPort) {
      message.warning('请选择串口');
      return;
    }

    setConnecting(true);
    try {
      await invoke('connect_serial', { portName: selectedPort });
      setConnectionStatus(true);
      message.success('连接成功');
    } catch (error) {
      message.error('连接失败: ' + error);
    } finally {
      setConnecting(false);
    }
  };

  const disconnect = async () => {
    setConnecting(true);
    try {
      await invoke('disconnect_serial');
      setConnectionStatus(false);
      setSignalStrength(0);
      message.success('已断开连接');
    } catch (error) {
      message.error('断开连接失败: ' + error);
    } finally {
      setConnecting(false);
    }
  };

  const handleMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'settings':
        setShowSettingsDialog(true);
        break;
      case 'about':
        message.info('GPDSMS-Rust v1.5.0\n基于Rust和Tauri开发的短信管理工具');
        break;
      case 'exit':
        // TODO: Close application
        break;
    }
  };

  const menuItems = [
    {
      key: 'settings',
      label: '设置',
    },
    {
      key: 'about',
      label: '关于',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'exit',
      label: '退出',
    },
  ];

  return (
    <Layout style={{ height: '100vh' }}>
      {/* Header */}
      <Header style={{
        background: '#1890ff',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '0 20px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <PhoneOutlined style={{ fontSize: '20px' }} />
          <Title level={4} style={{ color: 'white', margin: 0 }}>
            GPDSMS-Rust
          </Title>
        </div>

        <Space>
          <Badge count={unreadCount} size="small">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setShowSendDialog(true)}
              style={{ background: '#52c41a', borderColor: '#52c41a' }}
            >
              新短信
            </Button>
          </Badge>

          <Dropdown menu={{ items: menuItems, onClick: handleMenuClick }}>
            <Button type="text" icon={<SettingOutlined />} style={{ color: 'white' }} />
          </Dropdown>
        </Space>
      </Header>

      <Layout>
        {/* Sidebar */}
        <Sider
          width={300}
          style={{ background: '#f5f5f5', padding: '20px' }}
          collapsible={false}
        >
          {/* Connection Status */}
          <Card size="small" style={{ marginBottom: '16px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <WifiOutlined
                  style={{
                    color: connectionStatus ? '#52c41a' : '#ff4d4f',
                    fontSize: '16px'
                  }}
                />
                <span>{connectionStatus ? '已连接' : '未连接'}</span>
              </div>
              <Button
                size="small"
                type={connectionStatus ? 'default' : 'primary'}
                danger={connectionStatus}
                onClick={toggleConnection}
                loading={connecting}
              >
                {connectionStatus ? '断开' : '连接'}
              </Button>
            </div>
          </Card>

          {/* Port Selection */}
          {!connectionStatus && (
            <Card size="small" style={{ marginBottom: '16px' }}>
              <div style={{ marginBottom: '8px' }}>选择串口:</div>
              <Select
                value={selectedPort}
                onChange={setSelectedPort}
                onFocus={refreshPorts}
                style={{ width: '100%' }}
                placeholder="选择串口"
              >
                {availablePorts.map(port => (
                  <Option key={port.port_name} value={port.port_name}>
                    {port.port_name} {port.description && `(${port.description})`}
                  </Option>
                ))}
              </Select>
            </Card>
          )}

          {/* Signal Strength */}
          {connectionStatus && (
            <Card size="small">
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <SignalFilled style={{ color: '#1890ff' }} />
                <span>信号强度: {signalStrength}</span>
              </div>
            </Card>
          )}
        </Sider>

        {/* Main Content */}
        <Content style={{ padding: '20px', background: '#fff' }}>
          <MessageList />
        </Content>
      </Layout>

      {/* Dialogs */}
      <SendSmsDialog
        visible={showSendDialog}
        onClose={() => setShowSendDialog(false)}
        onSent={() => {
          message.success('短信发送成功');
          setShowSendDialog(false);
        }}
      />

      <SettingsDialog
        visible={showSettingsDialog}
        onClose={() => setShowSettingsDialog(false)}
      />
    </Layout>
  );
};

export default App;
