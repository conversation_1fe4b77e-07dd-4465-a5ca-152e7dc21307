mod core;
mod gui;
mod utils;

use crate::gui::app_state::AppState;
use tauri::Manager;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    env_logger::init();

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_fs::init())
        .setup(|app| {
            // Initialize app state
            let app_state = tauri::async_runtime::block_on(async {
                AppState::new().await.expect("Failed to initialize app state")
            });

            app.manage(app_state);

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            gui::commands::get_serial_ports,
            gui::commands::connect_serial,
            gui::commands::disconnect_serial,
            gui::commands::get_connection_status,
            gui::commands::test_connection,
            gui::commands::diagnose_connection,
            gui::commands::auto_connect_gsm,
            gui::commands::load_sms_from_sim,
            gui::commands::send_sms,
            gui::commands::get_sms_history,
            gui::commands::delete_sms,
            gui::commands::search_sms,
            gui::commands::get_signal_strength,
            gui::commands::get_network_status,
            gui::commands::check_new_sms,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
