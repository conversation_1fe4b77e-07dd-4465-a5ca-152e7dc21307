import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Input, 
  Button, 
  Tag, 
  Space, 
  Modal, 
  Descriptions, 
  Pagination,
  message,
  Popconfirm
} from 'antd';
import { 
  SearchOutlined, 
  ReloadOutlined, 
  MessageOutlined,
  DeleteOutlined 
} from '@ant-design/icons';
import { invoke } from '@tauri-apps/api/core';
import { useAppStore, SmsMessage } from '../store/appStore';

const { Search } = Input;

interface SmsHistoryResponse {
  messages: SmsMessage[];
  total_count: number;
}

const MessageList: React.FC = () => {
  const [searchText, setSearchText] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);
  const [totalMessages, setTotalMessages] = useState(0);
  const [selectedMessage, setSelectedMessage] = useState<SmsMessage | null>(null);
  const [detailVisible, setDetailVisible] = useState(false);
  
  const { 
    messages, 
    setMessages, 
    removeMessage,
    loading,
    setLoading 
  } = useAppStore();

  useEffect(() => {
    refreshMessages();
  }, [currentPage, pageSize]);

  const refreshMessages = async () => {
    setLoading('loadingMessages', true);
    try {
      const offset = (currentPage - 1) * pageSize;
      const response = await invoke<SmsHistoryResponse>('get_sms_history', {
        limit: pageSize,
        offset: offset
      });
      
      setMessages(response.messages);
      setTotalMessages(response.total_count);
    } catch (error) {
      message.error('获取短信历史失败: ' + error);
    } finally {
      setLoading('loadingMessages', false);
    }
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
    // TODO: Implement search functionality
  };

  const handleRowDoubleClick = (record: SmsMessage) => {
    setSelectedMessage(record);
    setDetailVisible(true);
  };

  const handleDelete = async (record: SmsMessage) => {
    try {
      await invoke('delete_sms', { id: record.id });
      removeMessage(record.id!);
      message.success('删除成功');
    } catch (error) {
      message.error('删除失败: ' + error);
    }
  };

  const handleReply = (record: SmsMessage) => {
    // TODO: Open send dialog with pre-filled phone number
    const phoneNumber = record.message_type === 'received' ? record.phone_no : record.phone_to;
    console.log('Reply to:', phoneNumber);
  };

  const truncateMessage = (text: string, maxLength: number = 50) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '联系人',
      key: 'contact',
      width: 150,
      render: (record: SmsMessage) => (
        <span>
          {record.message_type === 'received' ? record.phone_no : record.phone_to}
        </span>
      ),
    },
    {
      title: '内容',
      dataIndex: 'message',
      key: 'message',
      render: (text: string) => (
        <div className="message-preview">
          {truncateMessage(text)}
        </div>
      ),
    },
    {
      title: '时间',
      dataIndex: 'create_date',
      key: 'create_date',
      width: 180,
    },
    {
      title: '类型',
      key: 'message_type',
      width: 100,
      render: (record: SmsMessage) => (
        <Tag color={record.message_type === 'received' ? 'green' : 'blue'}>
          {record.message_type === 'received' ? '接收' : '发送'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (record: SmsMessage) => (
        <Space>
          <Button 
            size="small" 
            icon={<MessageOutlined />}
            onClick={() => handleReply(record)}
            disabled={record.message_type === 'sent'}
          >
            回复
          </Button>
          <Popconfirm
            title="确定要删除这条短信吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              size="small" 
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const filteredMessages = searchText 
    ? messages.filter(msg => 
        msg.message.toLowerCase().includes(searchText.toLowerCase()) ||
        msg.phone_no.includes(searchText) ||
        msg.phone_to.includes(searchText)
      )
    : messages;

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '20px',
        paddingBottom: '10px',
        borderBottom: '1px solid #e8e8e8'
      }}>
        <h2 style={{ margin: 0, color: '#303133' }}>短信历史</h2>
        <Space>
          <Search
            placeholder="搜索短信..."
            allowClear
            style={{ width: 200 }}
            onSearch={handleSearch}
          />
          <Button 
            icon={<ReloadOutlined />}
            onClick={refreshMessages}
            loading={loading.loadingMessages}
          >
            刷新
          </Button>
        </Space>
      </div>

      {/* Table */}
      <div style={{ flex: 1, minHeight: 0 }}>
        <Table
          columns={columns}
          dataSource={filteredMessages}
          rowKey="id"
          loading={loading.loadingMessages}
          pagination={false}
          scroll={{ y: 'calc(100vh - 300px)' }}
          onRow={(record) => ({
            onDoubleClick: () => handleRowDoubleClick(record),
          })}
          size="middle"
        />
      </div>

      {/* Pagination */}
      <div style={{ 
        marginTop: '20px', 
        display: 'flex', 
        justifyContent: 'center' 
      }}>
        <Pagination
          current={currentPage}
          pageSize={pageSize}
          total={totalMessages}
          showSizeChanger
          showQuickJumper
          showTotal={(total, range) => 
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          }
          onChange={(page, size) => {
            setCurrentPage(page);
            setPageSize(size || pageSize);
          }}
          pageSizeOptions={['20', '50', '100', '200']}
        />
      </div>

      {/* Detail Modal */}
      <Modal
        title="短信详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailVisible(false)}>
            关闭
          </Button>,
          selectedMessage?.message_type === 'received' && (
            <Button 
              key="reply" 
              type="primary"
              icon={<MessageOutlined />}
              onClick={() => {
                handleReply(selectedMessage);
                setDetailVisible(false);
              }}
            >
              回复
            </Button>
          ),
        ].filter(Boolean)}
        width={600}
      >
        {selectedMessage && (
          <Descriptions column={1} bordered>
            <Descriptions.Item label="联系人">
              {selectedMessage.message_type === 'received' 
                ? selectedMessage.phone_no 
                : selectedMessage.phone_to}
            </Descriptions.Item>
            <Descriptions.Item label="时间">
              {selectedMessage.create_date}
            </Descriptions.Item>
            <Descriptions.Item label="类型">
              <Tag color={selectedMessage.message_type === 'received' ? 'green' : 'blue'}>
                {selectedMessage.message_type === 'received' ? '接收' : '发送'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="内容">
              <div className="message-content-detail">
                {selectedMessage.message}
              </div>
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default MessageList;
