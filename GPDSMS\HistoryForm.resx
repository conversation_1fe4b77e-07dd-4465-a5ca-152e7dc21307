﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="imageList1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="imageList1.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        ********************************************************************************
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAC0
        BQAAAk1TRnQBSQFMAwEBAAE4AQABOAEAARABAAEQAQAE/wEhAQAI/wFCAU0BNgcAATYDAAEoAwABQAMA
        ARADAAEBAQABIAYAARAeAAMjATMDIwEzAwsBDvQAAbUBgwFIAf8BtAGCAUUB/wM/AWwDEwEa8AADTgGY
        Af8B+wHoAf8B1AG0AYwB/wNQAZoDFQEd6AADCAEKAy0BRQHSAbABhwX/AdEBsAGFAf8CVAFTAaYDIwEz
        AyMBMwMjATMDIwEzAxsBJgMIAQrMAAMOARIDNwFaAlwBWQHBAbwBigFOA/8B7wP/AfEB/wHOAaUBcwH/
        AbUBewFAAf8BtgGAAUMB/wG2AYABQwH/AbcBggFEAf8BYAFfAVwByQM4AV0DDgESxAADCAEKA0QBewG+
        AYoBUAH/AeoB0AGuAv8B9QHeAv8B9wHhAv8B9gHhAv8B+gHlAv8B/QHqAv8B/gHrA/8B7AL/AfkB5AH/
        AeoB0QGvAf8BvgGKAVAB/wNEAXsDCAEKwAADMwFSAcABjAFRAf8B+AHoAcsC/wH4AeMC/wH0AdoC/wHy
        AdgC/wHxAdcC/wHyAdkC/wHzAdkC/wHzAdkC/wHzAdoC/wH0Ad0C/wH4AeMB/wH4AegBywH/AcABjAFR
        Af8DMwFSwAABYAFfAV0ByQHrAdEBrgL/AfcB4QL/AfAB1AL/Ae4B0gL/Ae4B0gL/Ae4B0gL/Ae4B0gL/
        Ae4B0gL/Ae4B0gL/Ae4B0gL/Ae4B0gL/AfAB1AL/AfcB4QH/AesB0QGuAf8BYAFfAV0BycAAAcABiAFM
        Av8B/AHmAv8B7gHTAv8B6wHNAv8B6wHNAv8B6wHNAv8B6wHNAv8B6wHNAv8B6wHNAv8B6wHNAv8B6wHN
        Av8B6wHNAv8B6wHNAv8B7gHTAv8B/AHmAf8BwAGIAUwB/8AAAcEBigFMAv8B+AHkAv8B6gHMAv8B6AHI
        Av8B6AHIAv8B6AHIAv8B6AHIAv8B6AHIAv8B6AHIAv8B6AHIAv8B6AHIAv8B6AHIAv8B6AHIAv8B6gHM
        Av8B+AHkAf8BwQGKAUwB/8AAAcMBiwFNAv8B9wHkAv8B6AHJAv8B5QHCAv8B5QHDAv8B5QHDAv8B5QHD
        Av8B5QHDAv8B5QHDAv8B5QHDAv8B5QHDAv8B5QHDAv8B5QHCAv8B6AHJAv8B9wHkAf8BwwGLAU0B/8AA
        AcYBjgFRAv8B+QHoAv8B6QHKAv8B4wG8Av8B4wG+Av8B4wG+Av8B4wG+Av8B4wG+Av8B4wG+Av8B4wG+
        Av8B4wG+Av8B4wG+Av8B4wG8Av8B6QHKAv8B+QHoAf8BxgGOAVEB/8AAAVwCWgG/Ae4B0QGxAv8B9AHh
        Av8B4QG5Av8B3wG2Av8B3wG3Av8B3wG3Av8B3wG3Av8B3wG3Av8B3wG3Av8B3wG3Av8B3wG2Av8B4QG5
        Av8B9AHhAf8B7gHRAbEB/wFcAloBv8AAAyQBNAHOAZcBXQH/AfoB5gHQAv8B9AHjAv8B5gHEAv8B4gG9
        Av8B4gG9Av8B4gG9Av8B4gG9Av8B4gG9Av8B4gG9Av8B5gHEAv8B9AHjAf8B+gHmAdAB/wHOAZcBXQH/
        AyQBNMQAAzcBWgHQAZgBXgH/AfAB0wG1Av8B9AHkAv8B+wHtAv8B+gHtAv8B+gHtAv8B+gHtAv8B+gHt
        Av8B+wHtAv8B9AHkAf8B8AHTAbUB/wHQAZgBXgH/AzcBWswAAyQBNAJZAVgBvAHPAZQBWAH/Ac4BlAFX
        Af8BzgGTAVcB/wHOAZMBVwH/Ac4BkwFXAf8BzgGTAVcB/wHOAZQBVwH/Ac8BlAFYAf8CWQFYAbwDJAE0
        yAABQgFNAT4HAAE+AwABKAMAAUADAAEQAwABAQEAAQEFAAGAFwAD/wEAAeMB/wYAAeEB/wYAAeAB/wYA
        AcABAwYAAYABAU4AAYABAQYAAcABAwYACw==
</value>
  </data>
  <metadata name="menuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>153, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAgBAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAAAA0AAAAVAAAAFgAAABYAAAAWAAAAFgAA
        ABYAAAAWAAAAFgAAABYAAAAWAAAAFgAAABYAAAAWAAAAFgAAABUAAAANAAAABAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAANAAAAKwAAAD8AAABDAAAAQwAA
        AEMAAABDAAAAQwAAAEMAAABDAAAAQwAAAEMAAABDAAAAQwAAAEMAAABDAAAAPwAAACsAAAANAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABWKiIbEl5WS/5aU
        kf+WlJH/lpSR/5aUkf+WlJH/lpSR/5aUkf+WlJH/lpSR/5aUkf+WlJH/lpSR/5aUkf+XlZL/ioiGxAAA
        ABUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFpiW
        k//p6Oj/5+bl/+rp6P/r6un/6uno/+jn5v/q6ej/6+rp/+rp6P/o5+b/6uno/+vq6f/p6Oj/5ubl/+no
        6P+YlpP/AAAAFgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAWmJaT/+Pi4f/i4eD/eXd1/3x6eP+Ojo//5eTk/3p4df98enj/jo6P/+Xk5P96eHX/fHp4/46O
        jv/h4OD/4+Lh/5iWk/8AAAAWAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAABaZl5T/4d7e/97d3P+urKr/sq+t/46Ojv/i4eD/r62r/7Kvrf+Ojo7/4uHg/6+t
        q/+yr63/jY2O/9/e3f/h397/mZeU/wAAABYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAFpqYlf/f39z/29rZ/9/f3v/i4eD/4N/e/97d3P/g397/4uHg/+Df
        3v/e3dz/4N/e/+Lh4P/g397/29rZ/9/f3P+amJX/AAAAFgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWm5mW/97e3f/a2dj/eXd1/3x6eP+Pj5D/3t3c/3p4
        dv98enj/j4+Q/97d3P96eHb/fHp4/46Oj//a2dj/3t7d/5uZlv8AAAAWAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABadmpf/39zc/9fW1f+vrav/srCu/4+P
        j//b2tn/sK6s/7Kwrv+Pj4//29rZ/7CurP+ysK7/jo6P/9jX1v/d3Nz/nZqX/wAAABYAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFp6cmP/d3Nz/1NPS/9nY
        1//b2tn/2djX/9fW1f/Z2Nf/29rZ/9nY1//X1tX/2djX/9va2f/Z2Nf/1NPS/9zc2v+enJn/AAAAFgAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWn52a/97d
        2//T0tD/enh2/317ef+QkJH/19bU/3t5d/99e3n/kJCR/9fW1P97eXf/fXt5/4+QkP/T0tD/3Nvb/5+d
        mv8AAAAWAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        ABagnpv/3Nvb/9DPzf+vrav/srCu/4+PkP/T09H/sa+t/7Oxr/+QkJD/09LQ/7CurP+ysK7/jo+P/9DP
        zf/c29r/oJ6b/wAAABYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAFqGfnP/c29n/zsvJ/9DNy//Szs3/0s7N/9HPzf/U0tD/1tPR/9TS0P/Rzsz/0c/L/9LP
        y//Rz8v/zszJ/9zb2f+hn5z/AAAAFgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAWoqCd/93b2//OyMj/1cvM/9jMzv/WzM3/087N/3Jwbv90cnD/kpOT/9HP
        yf/U0cX/1tPF/9PRxf/Ny8T/3drZ/6Kgnf8AAAAWAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABajoZ7/3tza/9HIyP8XlGP/H5Zn/xmVZf/Wzc3/mJWT/5qX
        lf+UlJP/1NLH/zU6zf87P8z/NjnN/9DOwf/c29n/o6Ge/wAAABYAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFqSin//e3Nv/zsPE/yuqgP8zrIT/GJVk/9HI
        yP+4tbP/ube1/5CRkP/PzcH/XVjn/2Jd5v81Ocz/zcu//97d2f+kop//AAAAFgAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWpaOg/93c2v/Dvr3/yr/B/8zA
        w//KwML/w7+//8C/v//Av7//wcC//8LBvf/Hxbv/yci6/8jHu//Dwbz/3NrZ/6WjoP8AAAAWAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABampKH/3Nvb/9/e
        4P/6/f//+f7///j9///2/f//9vz///X7///2/P//9v3///f+///4////+v7//9/f4P/b2tj/pqSh/wAA
        ABYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFqel
        ov/d3dv/19vi/+CGE//mkRr/7Jse//OjHv/6qx///7If//utH//0pR7/7Z0e/+eTHP/hhxL/19vi/9va
        2v+npaL/AAAAFgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAWqaaj/93d3v/S2OD/4Ywf/+aaL//rny3/8agt//ivL//8tC//+bEv//OpLv/toi7/55or/+KM
        Hf/S2OD/3Nzb/6mno/8AAAAWAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAABaqqKT/3t7f/83S3P/fix//5Zw1/+ulPf/vpzL/9Kov//auL//1rC//8acw/+uh
        MP/mmC3/4Ywf/83S3P/c3d3/qqik/wAAABYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAFquppf/e3+D/yM7W/96IH//jlzH/6KM9/++tSP/xskv/8q9A//Gs
        Ov/toSv/55sr/+OVKP/eiBz/yM3U/93d3v+rqab/AAAAFgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWrKqm/+Dh4f/DydH/24Ug/9+SLv/lnDn/6qZB/+2t
        TP/vs1b/77Zf/+64af/uunL/6bFo/+enWf/Cx83/39/e/6yqp/8AAAAWAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABatq6n/4eLi/73Cy//YgiH/3I0t/+GW
        Nf/lnj7/6KVG/+qqTv/rrVf/6rFg/+myaP/ps3H/6K1r/7u/xP/g4OH/raup/wAAABYAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFq6sqf/k5OX/uL7E/9Z+
        Hv/ZiCv/3I4v/+CWOf/knkD/5qNJ/+enUv/nqVr/56xj/+etav/nqmj/t7q+/+Li4v+urKn/AAAAFgAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWr62r/+bm
        5v+0trr/0nQW/9Z+H//ZgyH/3osp/+GRMv/jmDz/5J1F/+agTv/kpFf/5qZf/+amY/+ztLb/4+Pj/6+t
        q/8AAAAWAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        ABWwrqz/5+bm/66trf+vsbX/rrO5/66yuf+tsrn/rbG4/62wt/+tsLb/rbC1/66wtf+vsrb/sLG0/7Cu
        rv/l5eT/sa6s/wAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAADbKwrv/n5+b/5+bm/+fn5//n5+j/5+fo/+fn6P/n5+j/5+fo/+fn6P/n5+j/6Ofo/6+u
        q/+mpaL/r62q/+fo5f+ysK7/AAAADQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAEsK6stLOxr/+zsa7/s7Cu/7Owrv+zsK7/s7Cu/7Owrv+zsK7/s7Cu/7Kw
        rv+wrqz/7ezs/93c2//t7Oz/sa+s/6mnpbIAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAFbKwrf/29vX/29rY//b39f+ysK7/AAAAFQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAANtLKv//38/f/7+vr//fz9/7SysP8AAAANAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAASzsa+0tbOx/7SysP+1s7H/s7GvtAAAAAQAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAA/gAAP/4AAD/+AAA//gAAP/4AAD/+AAA//gAAP/4AAD/+AAA//gAAP/4A
        AD/+AAA//gAAP/4AAD/+AAA//gAAP/4AAD/+AAA//gAAP/4AAD/+AAA//gAAP/4AAD/+AAA//gAAP/4A
        AD/+AAA//gAAP/4AAD///8B////Af///wH8=
</value>
  </data>
</root>