# Rust
/target
Cargo.lock

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Tauri
/src-tauri/target

# Build outputs
/dist
/dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Database
storage.db
*.db

# Logs
*.log

# OS generated files
Thumbs.db
.DS_Store

# Temporary files
*.tmp
*.temp

# Lock files
*.lock
