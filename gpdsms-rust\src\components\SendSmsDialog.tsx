import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Button, message, Space, Typography } from 'antd';
import { SendOutlined } from '@ant-design/icons';
import { invoke } from '@tauri-apps/api/core';
import { useAppStore } from '../store/appStore';

const { TextArea } = Input;
const { Text } = Typography;

interface SendSmsDialogProps {
  visible: boolean;
  onClose: () => void;
  onSent: () => void;
  phoneNumber?: string;
  messageContent?: string;
}

interface SendSmsRequest {
  phone_number: string;
  message: string;
}

const SendSmsDialog: React.FC<SendSmsDialogProps> = ({
  visible,
  onClose,
  onSent,
  phoneNumber = '',
  messageContent = ''
}) => {
  const [form] = Form.useForm();
  const [messageText, setMessageText] = useState('');
  const { loading, setLoading } = useAppStore();

  useEffect(() => {
    if (visible) {
      form.setFieldsValue({
        phoneNumber: phoneNumber,
        message: messageContent
      });
      setMessageText(messageContent);
    } else {
      form.resetFields();
      setMessageText('');
    }
  }, [visible, phoneNumber, messageContent, form]);

  const estimateSmsCount = (text: string): number => {
    if (!text) return 0;
    
    // Simple estimation: 70 characters per SMS for ASCII, 35 for Unicode
    const hasUnicode = /[^\x00-\x7F]/.test(text);
    const maxCharsPerSms = hasUnicode ? 35 : 70;
    
    return Math.ceil(text.length / maxCharsPerSms);
  };

  const handleSend = async () => {
    try {
      const values = await form.validateFields();
      
      setLoading('sendingSms', true);
      
      const request: SendSmsRequest = {
        phone_number: values.phoneNumber,
        message: values.message
      };
      
      await invoke('send_sms', { request });
      
      message.success('短信发送成功');
      onSent();
      
    } catch (error) {
      if (error && typeof error === 'object' && 'errorFields' in error) {
        // Form validation error
        return;
      }
      message.error('发送失败: ' + error);
    } finally {
      setLoading('sendingSms', false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setMessageText('');
    onClose();
  };

  const smsCount = estimateSmsCount(messageText);

  return (
    <Modal
      title="发送短信"
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button 
          key="send" 
          type="primary" 
          icon={<SendOutlined />}
          onClick={handleSend}
          loading={loading.sendingSms}
        >
          发送
        </Button>,
      ]}
      width={500}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        requiredMark={false}
      >
        <Form.Item
          label="手机号码"
          name="phoneNumber"
          rules={[
            { required: true, message: '请输入手机号码' },
            { 
              pattern: /^1[3-9]\d{9}$/, 
              message: '请输入正确的手机号码' 
            }
          ]}
        >
          <Input 
            placeholder="请输入手机号码"
            maxLength={11}
          />
        </Form.Item>
        
        <Form.Item
          label="短信内容"
          name="message"
          rules={[
            { required: true, message: '请输入短信内容' },
            { min: 1, max: 500, message: '短信内容长度在 1 到 500 个字符' }
          ]}
        >
          <TextArea
            placeholder="请输入短信内容"
            rows={6}
            maxLength={500}
            showCount
            onChange={(e) => setMessageText(e.target.value)}
          />
        </Form.Item>
        
        <Form.Item>
          <Space style={{ width: '100%', justifyContent: 'space-between' }}>
            <Text type="secondary">
              字符数: {messageText.length}/500
            </Text>
            <Text type="secondary">
              预计短信条数: {smsCount}
            </Text>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default SendSmsDialog;
