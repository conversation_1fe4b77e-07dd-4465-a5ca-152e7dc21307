﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="notifyIcon1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>122, 17</value>
  </metadata>
  <metadata name="contextMenuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>262, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="notifyIcon1.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAgBAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAABAAAAA0AAAAVAAAAFgAAABYAAAAWAAAAFgAAABYAAAAWAAAAFgAA
        ABYAAAAWAAAAFgAAABYAAAAWAAAAFgAAABUAAAANAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAANAAAAKwAAAD8AAABDAAAAQwAAAEMAAABDAAAAQwAA
        AEMAAABDAAAAQwAAAEMAAABDAAAAQwAAAEMAAABDAAAAPwAAACsAAAANAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABWKiIbEl5WS/5aUkf+WlJH/lpSR/5aU
        kf+WlJH/lpSR/5aUkf+WlJH/lpSR/5aUkf+WlJH/lpSR/5aUkf+XlZL/ioiGxAAAABUAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFpiWk//p6Oj/5+bl/+rp
        6P/r6un/6uno/+jn5v/q6ej/6+rp/+rp6P/o5+b/6uno/+vq6f/p6Oj/5ubl/+no6P+YlpP/AAAAFgAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWmJaT/+Pi
        4f/i4eD/eXd1/3x6eP+Ojo//5eTk/3p4df98enj/jo6P/+Xk5P96eHX/fHp4/46Ojv/h4OD/4+Lh/5iW
        k/8AAAAWAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        ABaZl5T/4d7e/97d3P+urKr/sq+t/46Ojv/i4eD/r62r/7Kvrf+Ojo7/4uHg/6+tq/+yr63/jY2O/9/e
        3f/h397/mZeU/wAAABYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAFpqYlf/f39z/29rZ/9/f3v/i4eD/4N/e/97d3P/g397/4uHg/+Df3v/e3dz/4N/e/+Lh
        4P/g397/29rZ/9/f3P+amJX/AAAAFgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAWm5mW/97e3f/a2dj/eXd1/3x6eP+Pj5D/3t3c/3p4dv98enj/j4+Q/97d
        3P96eHb/fHp4/46Oj//a2dj/3t7d/5uZlv8AAAAWAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABadmpf/39zc/9fW1f+vrav/srCu/4+Pj//b2tn/sK6s/7Kw
        rv+Pj4//29rZ/7CurP+ysK7/jo6P/9jX1v/d3Nz/nZqX/wAAABYAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFp6cmP/d3Nz/1NPS/9nY1//b2tn/2djX/9fW
        1f/Z2Nf/29rZ/9nY1//X1tX/2djX/9va2f/Z2Nf/1NPS/9zc2v+enJn/AAAAFgAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWn52a/97d2//T0tD/enh2/317
        ef+QkJH/19bU/3t5d/99e3n/kJCR/9fW1P97eXf/fXt5/4+QkP/T0tD/3Nvb/5+dmv8AAAAWAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABagnpv/3Nvb/9DP
        zf+vrav/srCu/4+PkP/T09H/sa+t/7Oxr/+QkJD/09LQ/7CurP+ysK7/jo+P/9DPzf/c29r/oJ6b/wAA
        ABYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFqGf
        nP/c29n/zsvJ/9DNy//Szs3/0s7N/9HPzf/U0tD/1tPR/9TS0P/Rzsz/0c/L/9LPy//Rz8v/zszJ/9zb
        2f+hn5z/AAAAFgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAWoqCd/93b2//OyMj/1cvM/9jMzv/WzM3/087N/3Jwbv90cnD/kpOT/9HPyf/U0cX/1tPF/9PR
        xf/Ny8T/3drZ/6Kgnf8AAAAWAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAABajoZ7/3tza/9HIyP8XlGP/H5Zn/xmVZf/Wzc3/mJWT/5qXlf+UlJP/1NLH/zU6
        zf87P8z/NjnN/9DOwf/c29n/o6Ge/wAAABYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAFqSin//e3Nv/zsPE/yuqgP8zrIT/GJVk/9HIyP+4tbP/ube1/5CR
        kP/PzcH/XVjn/2Jd5v81Ocz/zcu//97d2f+kop//AAAAFgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWpaOg/93c2v/Dvr3/yr/B/8zAw//KwML/w7+//8C/
        v//Av7//wcC//8LBvf/Hxbv/yci6/8jHu//Dwbz/3NrZ/6WjoP8AAAAWAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABampKH/3Nvb/9/e4P/6/f//+f7///j9
        ///2/f//9vz///X7///2/P//9v3///f+///4////+v7//9/f4P/b2tj/pqSh/wAAABYAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFqelov/d3dv/19vi/+CG
        E//mkRr/7Jse//OjHv/6qx///7If//utH//0pR7/7Z0e/+eTHP/hhxL/19vi/9va2v+npaL/AAAAFgAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWqaaj/93d
        3v/S2OD/4Ywf/+aaL//rny3/8agt//ivL//8tC//+bEv//OpLv/toi7/55or/+KMHf/S2OD/3Nzb/6mn
        o/8AAAAWAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        ABaqqKT/3t7f/83S3P/fix//5Zw1/+ulPf/vpzL/9Kov//auL//1rC//8acw/+uhMP/mmC3/4Ywf/83S
        3P/c3d3/qqik/wAAABYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAFquppf/e3+D/yM7W/96IH//jlzH/6KM9/++tSP/xskv/8q9A//GsOv/toSv/55sr/+OV
        KP/eiBz/yM3U/93d3v+rqab/AAAAFgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAWrKqm/+Dh4f/DydH/24Ug/9+SLv/lnDn/6qZB/+2tTP/vs1b/77Zf/+64
        af/uunL/6bFo/+enWf/Cx83/39/e/6yqp/8AAAAWAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABatq6n/4eLi/73Cy//YgiH/3I0t/+GWNf/lnj7/6KVG/+qq
        Tv/rrVf/6rFg/+myaP/ps3H/6K1r/7u/xP/g4OH/raup/wAAABYAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFq6sqf/k5OX/uL7E/9Z+Hv/ZiCv/3I4v/+CW
        Of/knkD/5qNJ/+enUv/nqVr/56xj/+etav/nqmj/t7q+/+Li4v+urKn/AAAAFgAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWr62r/+bm5v+0trr/0nQW/9Z+
        H//ZgyH/3osp/+GRMv/jmDz/5J1F/+agTv/kpFf/5qZf/+amY/+ztLb/4+Pj/6+tq/8AAAAWAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABWwrqz/5+bm/66t
        rf+vsbX/rrO5/66yuf+tsrn/rbG4/62wt/+tsLb/rbC1/66wtf+vsrb/sLG0/7Curv/l5eT/sa6s/wAA
        ABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADbKw
        rv/n5+b/5+bm/+fn5//n5+j/5+fo/+fn6P/n5+j/5+fo/+fn6P/n5+j/6Ofo/6+uq/+mpaL/r62q/+fo
        5f+ysK7/AAAADQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAEsK6stLOxr/+zsa7/s7Cu/7Owrv+zsK7/s7Cu/7Owrv+zsK7/s7Cu/7Kwrv+wrqz/7ezs/93c
        2//t7Oz/sa+s/6mnpbIAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFbKw
        rf/29vX/29rY//b39f+ysK7/AAAAFQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAANtLKv//38/f/7+vr//fz9/7SysP8AAAANAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAASzsa+0tbOx/7SysP+1s7H/s7GvtAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAA8AAB//AAAf/wAAH/8AAB//AAAf/wAAH/8AAB//AAAf/wAAH/8AAB//AA
        Af/wAAH/8AAB//AAAf/wAAH/8AAB//AAAf/wAAH/8AAB//AAAf/wAAH/8AAB//AAAf/wAAH/8AAB//AA
        Af/wAAH/8AAB//AAAf///gP///4D///+A/8=
</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAgBAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAAAA0AAAAVAAAAFgAAABYAAAAWAAAAFgAA
        ABYAAAAWAAAAFgAAABYAAAAWAAAAFgAAABYAAAAWAAAAFgAAABUAAAANAAAABAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAANAAAAKwAAAD8AAABDAAAAQwAA
        AEMAAABDAAAAQwAAAEMAAABDAAAAQwAAAEMAAABDAAAAQwAAAEMAAABDAAAAPwAAACsAAAANAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABWKiIbEl5WS/5aU
        kf+WlJH/lpSR/5aUkf+WlJH/lpSR/5aUkf+WlJH/lpSR/5aUkf+WlJH/lpSR/5aUkf+XlZL/ioiGxAAA
        ABUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFpiW
        k//p6Oj/5+bl/+rp6P/r6un/6uno/+jn5v/q6ej/6+rp/+rp6P/o5+b/6uno/+vq6f/p6Oj/5ubl/+no
        6P+YlpP/AAAAFgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAWmJaT/+Pi4f/i4eD/eXd1/3x6eP+Ojo//5eTk/3p4df98enj/jo6P/+Xk5P96eHX/fHp4/46O
        jv/h4OD/4+Lh/5iWk/8AAAAWAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAABaZl5T/4d7e/97d3P+urKr/sq+t/46Ojv/i4eD/r62r/7Kvrf+Ojo7/4uHg/6+t
        q/+yr63/jY2O/9/e3f/h397/mZeU/wAAABYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAFpqYlf/f39z/29rZ/9/f3v/i4eD/4N/e/97d3P/g397/4uHg/+Df
        3v/e3dz/4N/e/+Lh4P/g397/29rZ/9/f3P+amJX/AAAAFgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWm5mW/97e3f/a2dj/eXd1/3x6eP+Pj5D/3t3c/3p4
        dv98enj/j4+Q/97d3P96eHb/fHp4/46Oj//a2dj/3t7d/5uZlv8AAAAWAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABadmpf/39zc/9fW1f+vrav/srCu/4+P
        j//b2tn/sK6s/7Kwrv+Pj4//29rZ/7CurP+ysK7/jo6P/9jX1v/d3Nz/nZqX/wAAABYAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFp6cmP/d3Nz/1NPS/9nY
        1//b2tn/2djX/9fW1f/Z2Nf/29rZ/9nY1//X1tX/2djX/9va2f/Z2Nf/1NPS/9zc2v+enJn/AAAAFgAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWn52a/97d
        2//T0tD/enh2/317ef+QkJH/19bU/3t5d/99e3n/kJCR/9fW1P97eXf/fXt5/4+QkP/T0tD/3Nvb/5+d
        mv8AAAAWAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        ABagnpv/3Nvb/9DPzf+vrav/srCu/4+PkP/T09H/sa+t/7Oxr/+QkJD/09LQ/7CurP+ysK7/jo+P/9DP
        zf/c29r/oJ6b/wAAABYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAFqGfnP/c29n/zsvJ/9DNy//Szs3/0s7N/9HPzf/U0tD/1tPR/9TS0P/Rzsz/0c/L/9LP
        y//Rz8v/zszJ/9zb2f+hn5z/AAAAFgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAWoqCd/93b2//OyMj/1cvM/9jMzv/WzM3/087N/3Jwbv90cnD/kpOT/9HP
        yf/U0cX/1tPF/9PRxf/Ny8T/3drZ/6Kgnf8AAAAWAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABajoZ7/3tza/9HIyP8XlGP/H5Zn/xmVZf/Wzc3/mJWT/5qX
        lf+UlJP/1NLH/zU6zf87P8z/NjnN/9DOwf/c29n/o6Ge/wAAABYAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFqSin//e3Nv/zsPE/yuqgP8zrIT/GJVk/9HI
        yP+4tbP/ube1/5CRkP/PzcH/XVjn/2Jd5v81Ocz/zcu//97d2f+kop//AAAAFgAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWpaOg/93c2v/Dvr3/yr/B/8zA
        w//KwML/w7+//8C/v//Av7//wcC//8LBvf/Hxbv/yci6/8jHu//Dwbz/3NrZ/6WjoP8AAAAWAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABampKH/3Nvb/9/e
        4P/6/f//+f7///j9///2/f//9vz///X7///2/P//9v3///f+///4////+v7//9/f4P/b2tj/pqSh/wAA
        ABYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFqel
        ov/d3dv/19vi/+CGE//mkRr/7Jse//OjHv/6qx///7If//utH//0pR7/7Z0e/+eTHP/hhxL/19vi/9va
        2v+npaL/AAAAFgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAWqaaj/93d3v/S2OD/4Ywf/+aaL//rny3/8agt//ivL//8tC//+bEv//OpLv/toi7/55or/+KM
        Hf/S2OD/3Nzb/6mno/8AAAAWAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAABaqqKT/3t7f/83S3P/fix//5Zw1/+ulPf/vpzL/9Kov//auL//1rC//8acw/+uh
        MP/mmC3/4Ywf/83S3P/c3d3/qqik/wAAABYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAFquppf/e3+D/yM7W/96IH//jlzH/6KM9/++tSP/xskv/8q9A//Gs
        Ov/toSv/55sr/+OVKP/eiBz/yM3U/93d3v+rqab/AAAAFgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWrKqm/+Dh4f/DydH/24Ug/9+SLv/lnDn/6qZB/+2t
        TP/vs1b/77Zf/+64af/uunL/6bFo/+enWf/Cx83/39/e/6yqp/8AAAAWAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABatq6n/4eLi/73Cy//YgiH/3I0t/+GW
        Nf/lnj7/6KVG/+qqTv/rrVf/6rFg/+myaP/ps3H/6K1r/7u/xP/g4OH/raup/wAAABYAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFq6sqf/k5OX/uL7E/9Z+
        Hv/ZiCv/3I4v/+CWOf/knkD/5qNJ/+enUv/nqVr/56xj/+etav/nqmj/t7q+/+Li4v+urKn/AAAAFgAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWr62r/+bm
        5v+0trr/0nQW/9Z+H//ZgyH/3osp/+GRMv/jmDz/5J1F/+agTv/kpFf/5qZf/+amY/+ztLb/4+Pj/6+t
        q/8AAAAWAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        ABWwrqz/5+bm/66trf+vsbX/rrO5/66yuf+tsrn/rbG4/62wt/+tsLb/rbC1/66wtf+vsrb/sLG0/7Cu
        rv/l5eT/sa6s/wAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAADbKwrv/n5+b/5+bm/+fn5//n5+j/5+fo/+fn6P/n5+j/5+fo/+fn6P/n5+j/6Ofo/6+u
        q/+mpaL/r62q/+fo5f+ysK7/AAAADQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAEsK6stLOxr/+zsa7/s7Cu/7Owrv+zsK7/s7Cu/7Owrv+zsK7/s7Cu/7Kw
        rv+wrqz/7ezs/93c2//t7Oz/sa+s/6mnpbIAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAFbKwrf/29vX/29rY//b39f+ysK7/AAAAFQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAANtLKv//38/f/7+vr//fz9/7SysP8AAAANAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAASzsa+0tbOx/7SysP+1s7H/s7GvtAAAAAQAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAA/gAAP/4AAD/+AAA//gAAP/4AAD/+AAA//gAAP/4AAD/+AAA//gAAP/4A
        AD/+AAA//gAAP/4AAD/+AAA//gAAP/4AAD/+AAA//gAAP/4AAD/+AAA//gAAP/4AAD/+AAA//gAAP/4A
        AD/+AAA//gAAP/4AAD///8B////Af///wH8=
</value>
  </data>
</root>